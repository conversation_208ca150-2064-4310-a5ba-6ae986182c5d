#ifndef GLTF_H
#define GLTF_H

#include <string>
#include <vector>
#include <map>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>

#include "json.h" // Our custom JSON parser

namespace Gltf {

// Forward declarations
struct Node;
struct Mesh;
struct Material;
struct Buffer;
struct BufferView;
struct Accessor;
struct Animation;
struct Skin;

// Basic types
enum class ComponentType {
    BYTE = 5120,
    UNSIGNED_BYTE = 5121,
    SHORT = 5122,
    UNSIGNED_SHORT = 5123,
    UNSIGNED_INT = 5125,
    FLOAT = 5126
};

enum class Type {
    SCALAR,
    VEC2,
    VEC3,
    VEC4,
    MAT2,
    MAT3,
    MAT4
};

struct Asset {
    std::string version;
    std::string generator;
};

struct Buffer {
    std::string uri;
    size_t byteLength;
    std::vector<unsigned char> data; // Raw binary data
};

struct BufferView {
    int buffer;
    size_t byteOffset;
    size_t byteLength;
    int target; // 34962 (ARRAY_BUFFER) or 34963 (ELEMENT_ARRAY_BUFFER)
};

struct Accessor {
    int bufferView;
    size_t byteOffset;
    ComponentType componentType;
    bool normalized;
    size_t count;
    Type type;
    std::vector<double> min;
    std::vector<double> max;
};

struct Image {
    std::string uri;
    std::string mimeType;
    int bufferView = -1; // Optional
};

struct Sampler {
    int magFilter = -1; // Optional
    int minFilter = -1; // Optional
    int wrapS = 10497; // CLAMP_TO_EDGE
    int wrapT = 10497; // CLAMP_TO_EDGE
};

struct Texture {
    int sampler = -1; // Optional
    int source = -1; // Optional (image index)
};

struct TextureInfo {
    int index;
    int texCoord = 0; // Optional
};

struct MaterialPBRMetallicRoughness {
    glm::vec4 baseColorFactor = glm::vec4(1.0f);
    TextureInfo baseColorTexture; // Optional
    float metallicFactor = 1.0f;
    float roughnessFactor = 1.0f;
    TextureInfo metallicRoughnessTexture; // Optional
};

struct Material {
    std::string name;
    MaterialPBRMetallicRoughness pbrMetallicRoughness;
    TextureInfo normalTexture; // Optional
    TextureInfo occlusionTexture; // Optional
    TextureInfo emissiveTexture; // Optional
    glm::vec3 emissiveFactor = glm::vec3(0.0f);
    std::string alphaMode = "OPAQUE";
    float alphaCutoff = 0.5f;
    bool doubleSided = false;
};

struct Primitive {
    std::map<std::string, int> attributes; // e.g., "POSITION": 0, "NORMAL": 1
    int indices = -1; // Optional
    int material = -1; // Optional
    int mode = 4; // TRIANGLES
};

struct Mesh {
    std::string name;
    std::vector<Primitive> primitives;
};

struct Node {
    std::string name;
    int mesh = -1; // Optional
    int skin = -1; // Optional
    int camera = -1; // Optional
    std::vector<int> children; // Optional
    glm::vec3 translation = glm::vec3(0.0f);
    glm::quat rotation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f); // w, x, y, z
    glm::vec3 scale = glm::vec3(1.0f);
    std::vector<float> matrix; // Optional, if present, overrides translation, rotation, scale
};

struct Scene {
    std::string name;
    std::vector<int> nodes;
};

struct GltfModel {
    Asset asset;
    std::vector<Scene> scenes;
    int scene = -1; // Default scene
    std::vector<Node> nodes;
    std::vector<Mesh> meshes;
    std::vector<Material> materials;
    std::vector<Buffer> buffers;
    std::vector<BufferView> bufferViews;
    std::vector<Accessor> accessors;
    std::vector<Image> images;
    std::vector<Sampler> samplers;
    std::vector<Texture> textures;
    struct AnimationChannelTarget {
    int node = -1; // The index of the node to animate.
    std::string path; // The name of the node's property to animate ("translation", "rotation", "scale", "weights").
};

struct AnimationChannel {
    int sampler; // The index of the sampler.
    AnimationChannelTarget target;
};

struct AnimationSampler {
    int input; // The index of an accessor containing keyframe input values, e.g., time.
    std::string interpolation = "LINEAR"; // Interpolation algorithm.
    int output; // The index of an accessor containing keyframe output values.
};

struct Animation {
    std::string name; // Optional user-defined name for this object.
    std::vector<AnimationChannel> channels;
    std::vector<AnimationSampler> samplers;
};

struct Skin {
    std::string name; // Optional user-defined name for this object.
    int inverseBindMatrices = -1; // The index of the accessor containing inverse bind matrices.
    int skeleton = -1; // The index of the node used as a skeleton root.
    std::vector<int> joints; // Indices of nodes that are joints in this skin.
};
};

class GltfParser {
public:
    static GltfModel parse(const std::string& gltfJsonString, const std::string& baseDir = "");

private:
    static void parseAsset(const JsonValue& jsonAsset, Asset& asset);
    static void parseBuffer(const JsonValue& jsonBuffer, Buffer& buffer, const std::string& baseDir);
    static void parseBufferView(const JsonValue& jsonBufferView, BufferView& bufferView);
    static void parseAccessor(const JsonValue& jsonAccessor, Accessor& accessor);
    static void parseImage(const JsonValue& jsonImage, Image& image);
    static void parseSampler(const JsonValue& jsonSampler, Sampler& sampler);
    static void parseTexture(const JsonValue& jsonTexture, Texture& texture);
    static void parseTextureInfo(const JsonValue& jsonTextureInfo, TextureInfo& textureInfo);
    static void parseMaterialPBRMetallicRoughness(const JsonValue& jsonPbr, MaterialPBRMetallicRoughness& pbr);
    static void parseMaterial(const JsonValue& jsonMaterial, Material& material);
    static void parsePrimitive(const JsonValue& jsonPrimitive, Primitive& primitive);
    static void parseMesh(const JsonValue& jsonMesh, Mesh& mesh);
    static void parseNode(const JsonValue& jsonNode, Node& node);
    static void parseScene(const JsonValue& jsonScene, Scene& scene);

    static ComponentType stringToComponentType(const std::string& str);
    static Type stringToType(const std::string& str);
    static size_t getComponentTypeSize(ComponentType type);
    static size_t getTypeCount(Type type);

    // Base64 decoding utility
    static std::vector<unsigned char> base64Decode(const std::string& encoded_string);
};

} // namespace Gltf

#endif // GLTF_H
