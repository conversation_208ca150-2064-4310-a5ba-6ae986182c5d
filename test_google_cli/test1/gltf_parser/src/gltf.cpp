#include "gltf.h"
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <glm/gtc/type_ptr.hpp>
#include "logger.h"
#include "error_handler.h"

namespace Gltf {

// Utility functions
ComponentType GltfParser::stringToComponentType(const std::string& str) {
    if (str == "5120") return ComponentType::BYTE;
    if (str == "5121") return ComponentType::UNSIGNED_BYTE;
    if (str == "5122") return ComponentType::SHORT;
    if (str == "5123") return ComponentType::UNSIGNED_SHORT;
    if (str == "5125") return ComponentType::UNSIGNED_INT;
    if (str == "5126") return ComponentType::FLOAT;
    Utils::ErrorHandler::getInstance().handleError("Unknown component type: " + str, true, __FILE__, __LINE__);
    return ComponentType::BYTE; // Should not be reached if fatal error exits
}

Type GltfParser::stringToType(const std::string& str) {
    if (str == "SCALAR") return Type::SCALAR;
    if (str == "VEC2") return Type::VEC2;
    if (str == "VEC3") return Type::VEC3;
    if (str == "VEC4") return Type::VEC4;
    if (str == "MAT2") return Type::MAT2;
    if (str == "MAT3") return Type::MAT3;
    if (str == "MAT4") return Type::MAT4;
    Utils::ErrorHandler::getInstance().handleError("Unknown type: " + str, true, __FILE__, __LINE__);
    return Type::SCALAR; // Should not be reached if fatal error exits
}

size_t GltfParser::getComponentTypeSize(ComponentType type) {
    switch (type) {
        case ComponentType::BYTE: return 1;
        case ComponentType::UNSIGNED_BYTE: return 1;
        case ComponentType::SHORT: return 2;
        case ComponentType::UNSIGNED_SHORT: return 2;
        case ComponentType::UNSIGNED_INT: return 4;
        case ComponentType::FLOAT: return 4;
        default: throw std::runtime_error("Invalid component type");
    }
}

size_t GltfParser::getTypeCount(Type type) {
    switch (type) {
        case Type::SCALAR: return 1;
        case Type::VEC2: return 2;
        case Type::VEC3: return 3;
        case Type::VEC4: return 4;
        case Type::MAT2: return 4;
        case Type::MAT3: return 9;
        case Type::MAT4: return 16;
        default: throw std::runtime_error("Invalid type");
    }
}

// Parsing functions
void GltfParser::parseAsset(const JsonValue& jsonAsset, Asset& asset) {
    if (jsonAsset.asObject().count("version")) {
        asset.version = jsonAsset["version"].asString();
    }
    if (jsonAsset.asObject().count("generator")) {
        asset.generator = jsonAsset["generator"].asString();
    }
}

void GltfParser::parseBuffer(const JsonValue& jsonBuffer, Buffer& buffer, const std::string& baseDir) {
    if (jsonBuffer.asObject().count("uri")) {
        buffer.uri = jsonBuffer["uri"].asString();
    }
    if (jsonBuffer.asObject().count("byteLength")) {
        buffer.byteLength = static_cast<size_t>(jsonBuffer["byteLength"].asNumber());
    }

    // Load binary data if URI is present and not a data URI
    if (!buffer.uri.empty() && buffer.uri.rfind("data:", 0) != 0) {
        std::string fullPath = baseDir + buffer.uri;
        std::ifstream file(fullPath, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            Utils::ErrorHandler::getInstance().handleError("Failed to open buffer file: " + fullPath, true, __FILE__, __LINE__);
        }
        std::streamsize size = file.tellg();
        file.seekg(0, std::ios::beg);

        if (size != buffer.byteLength) {
            // This is a warning, not an error, as some glTF files might have padding
            Utils::Logger::getInstance().log(Utils::Logger::WARNING, "Buffer byteLength mismatch for " + buffer.uri, __FILE__, __LINE__);
        }

        buffer.data.resize(size);
        if (!file.read(reinterpret_cast<char*>(buffer.data.data()), size)) {
            Utils::ErrorHandler::getInstance().handleError("Failed to read buffer file: " + fullPath, true, __FILE__, __LINE__);
        }
    } else if (buffer.uri.rfind("data:", 0) == 0) {
        // Handle data URIs (base64 encoded binary data)
        // This is a simplified implementation and might not cover all cases
        size_t commaPos = buffer.uri.find(",");
        if (commaPos != std::string::npos) {
            std::string encodedData = buffer.uri.substr(commaPos + 1);
            // Basic base64 decode (not a full implementation)
            // For a real parser, you'd need a proper base64 decoder.
            // For now, we'll just assume it's direct binary if not base64
            // and warn if it's base64 but we don't decode.
            if (buffer.uri.find(";base64") != std::string::npos) {
                Utils::Logger::getInstance().log(Utils::Logger::INFO, "Decoding base64 data, encoded length: " + std::to_string(encodedData.length()), __FILE__, __LINE__);
                buffer.data = base64Decode(encodedData);
                Utils::Logger::getInstance().log(Utils::Logger::INFO, "Base64 decoded successfully, decoded length: " + std::to_string(buffer.data.size()), __FILE__, __LINE__);

                // Debug first few bytes
                std::string debugBytes = "First 16 bytes: ";
                for (size_t i = 0; i < std::min(buffer.data.size(), size_t(16)); ++i) {
                    debugBytes += std::to_string(static_cast<int>(buffer.data[i])) + " ";
                }
                Utils::Logger::getInstance().log(Utils::Logger::TRACE, debugBytes, __FILE__, __LINE__);
            } else {
                // Assume direct binary data if not base64 encoded
                buffer.data.assign(encodedData.begin(), encodedData.end());
            }
        }
    }
}

void GltfParser::parseBufferView(const JsonValue& jsonBufferView, BufferView& bufferView) {
    if (jsonBufferView.asObject().count("buffer")) {
        bufferView.buffer = static_cast<int>(jsonBufferView["buffer"].asNumber());
    }
    if (jsonBufferView.asObject().count("byteOffset")) {
        bufferView.byteOffset = static_cast<size_t>(jsonBufferView["byteOffset"].asNumber());
    }
    if (jsonBufferView.asObject().count("byteLength")) {
        bufferView.byteLength = static_cast<size_t>(jsonBufferView["byteLength"].asNumber());
    }
    if (jsonBufferView.asObject().count("target")) {
        bufferView.target = static_cast<int>(jsonBufferView["target"].asNumber());
    }
}

void GltfParser::parseAccessor(const JsonValue& jsonAccessor, Accessor& accessor) {
    if (jsonAccessor.asObject().count("bufferView")) {
        accessor.bufferView = static_cast<int>(jsonAccessor["bufferView"].asNumber());
    }
    if (jsonAccessor.asObject().count("byteOffset")) {
        accessor.byteOffset = static_cast<size_t>(jsonAccessor["byteOffset"].asNumber());
    }
    if (jsonAccessor.asObject().count("componentType")) {
        accessor.componentType = stringToComponentType(std::to_string(static_cast<int>(jsonAccessor["componentType"].asNumber())));
    }
    if (jsonAccessor.asObject().count("normalized")) {
        accessor.normalized = jsonAccessor["normalized"].asBool();
    }
    if (jsonAccessor.asObject().count("count")) {
        accessor.count = static_cast<size_t>(jsonAccessor["count"].asNumber());
    }
    if (jsonAccessor.asObject().count("type")) {
        accessor.type = stringToType(jsonAccessor["type"].asString());
    }
    if (jsonAccessor.asObject().count("min")) {
        for (const auto& val : jsonAccessor["min"].asArray()) {
            accessor.min.push_back(val.asNumber());
        }
    }
    if (jsonAccessor.asObject().count("max")) {
        for (const auto& val : jsonAccessor["max"].asArray()) {
            accessor.max.push_back(val.asNumber());
        }
    }
}

void GltfParser::parseImage(const JsonValue& jsonImage, Image& image) {
    if (jsonImage.asObject().count("uri")) {
        image.uri = jsonImage["uri"].asString();
    }
    if (jsonImage.asObject().count("mimeType")) {
        image.mimeType = jsonImage["mimeType"].asString();
    }
    if (jsonImage.asObject().count("bufferView")) {
        image.bufferView = static_cast<int>(jsonImage["bufferView"].asNumber());
    }
}

void GltfParser::parseSampler(const JsonValue& jsonSampler, Sampler& sampler) {
    if (jsonSampler.asObject().count("magFilter")) {
        sampler.magFilter = static_cast<int>(jsonSampler["magFilter"].asNumber());
    }
    if (jsonSampler.asObject().count("minFilter")) {
        sampler.minFilter = static_cast<int>(jsonSampler["minFilter"].asNumber());
    }
    if (jsonSampler.asObject().count("wrapS")) {
        sampler.wrapS = static_cast<int>(jsonSampler["wrapS"].asNumber());
    }
    if (jsonSampler.asObject().count("wrapT")) {
        sampler.wrapT = static_cast<int>(jsonSampler["wrapT"].asNumber());
    }
}

void GltfParser::parseTexture(const JsonValue& jsonTexture, Texture& texture) {
    if (jsonTexture.asObject().count("sampler")) {
        texture.sampler = static_cast<int>(jsonTexture["sampler"].asNumber());
    }
    if (jsonTexture.asObject().count("source")) {
        texture.source = static_cast<int>(jsonTexture["source"].asNumber());
    }
}

void GltfParser::parseTextureInfo(const JsonValue& jsonTextureInfo, TextureInfo& textureInfo) {
    if (jsonTextureInfo.asObject().count("index")) {
        textureInfo.index = static_cast<int>(jsonTextureInfo["index"].asNumber());
    }
    if (jsonTextureInfo.asObject().count("texCoord")) {
        textureInfo.texCoord = static_cast<int>(jsonTextureInfo["texCoord"].asNumber());
    }
}

void GltfParser::parseMaterialPBRMetallicRoughness(const JsonValue& jsonPbr, MaterialPBRMetallicRoughness& pbr) {
    if (jsonPbr.asObject().count("baseColorFactor")) {
        const JsonArray& factor = jsonPbr["baseColorFactor"].asArray();
        pbr.baseColorFactor = glm::vec4(factor[0].asNumber(), factor[1].asNumber(), factor[2].asNumber(), factor[3].asNumber());
    }
    if (jsonPbr.asObject().count("baseColorTexture")) {
        parseTextureInfo(jsonPbr["baseColorTexture"], pbr.baseColorTexture);
    }
    if (jsonPbr.asObject().count("metallicFactor")) {
        pbr.metallicFactor = static_cast<float>(jsonPbr["metallicFactor"].asNumber());
    }
    if (jsonPbr.asObject().count("roughnessFactor")) {
        pbr.roughnessFactor = static_cast<float>(jsonPbr["roughnessFactor"].asNumber());
    }
    if (jsonPbr.asObject().count("metallicRoughnessTexture")) {
        parseTextureInfo(jsonPbr["metallicRoughnessTexture"], pbr.metallicRoughnessTexture);
    }
}

void GltfParser::parseMaterial(const JsonValue& jsonMaterial, Material& material) {
    if (jsonMaterial.asObject().count("name")) {
        material.name = jsonMaterial["name"].asString();
    }
    if (jsonMaterial.asObject().count("pbrMetallicRoughness")) {
        parseMaterialPBRMetallicRoughness(jsonMaterial["pbrMetallicRoughness"], material.pbrMetallicRoughness);
    }
    if (jsonMaterial.asObject().count("normalTexture")) {
        parseTextureInfo(jsonMaterial["normalTexture"], material.normalTexture);
    }
    if (jsonMaterial.asObject().count("occlusionTexture")) {
        parseTextureInfo(jsonMaterial["occlusionTexture"], material.occlusionTexture);
    }
    if (jsonMaterial.asObject().count("emissiveTexture")) {
        parseTextureInfo(jsonMaterial["emissiveTexture"], material.emissiveTexture);
    }
    if (jsonMaterial.asObject().count("emissiveFactor")) {
        const JsonArray& factor = jsonMaterial["emissiveFactor"].asArray();
        material.emissiveFactor = glm::vec3(factor[0].asNumber(), factor[1].asNumber(), factor[2].asNumber());
    }
    if (jsonMaterial.asObject().count("alphaMode")) {
        material.alphaMode = jsonMaterial["alphaMode"].asString();
    }
    if (jsonMaterial.asObject().count("alphaCutoff")) {
        material.alphaCutoff = static_cast<float>(jsonMaterial["alphaCutoff"].asNumber());
    }
    if (jsonMaterial.asObject().count("doubleSided")) {
        material.doubleSided = jsonMaterial["doubleSided"].asBool();
    }
}

void GltfParser::parsePrimitive(const JsonValue& jsonPrimitive, Primitive& primitive) {
    if (jsonPrimitive.asObject().count("attributes")) {
        const JsonObject& attributes = jsonPrimitive["attributes"].asObject();
        for (const auto& pair : attributes) {
            primitive.attributes[pair.first] = static_cast<int>(pair.second.asNumber());
        }
    }
    if (jsonPrimitive.asObject().count("indices")) {
        primitive.indices = static_cast<int>(jsonPrimitive["indices"].asNumber());
    }
    if (jsonPrimitive.asObject().count("material")) {
        primitive.material = static_cast<int>(jsonPrimitive["material"].asNumber());
    }
    if (jsonPrimitive.asObject().count("mode")) {
        primitive.mode = static_cast<int>(jsonPrimitive["mode"].asNumber());
    }
}

void GltfParser::parseMesh(const JsonValue& jsonMesh, Mesh& mesh) {
    if (jsonMesh.asObject().count("name")) {
        mesh.name = jsonMesh["name"].asString();
    }
    if (jsonMesh.asObject().count("primitives")) {
        for (const auto& jsonPrimitive : jsonMesh["primitives"].asArray()) {
            Primitive primitive;
            parsePrimitive(jsonPrimitive, primitive);
            mesh.primitives.push_back(primitive);
        }
    }
}

void GltfParser::parseNode(const JsonValue& jsonNode, Node& node) {
    if (jsonNode.asObject().count("name")) {
        node.name = jsonNode["name"].asString();
    }
    if (jsonNode.asObject().count("mesh")) {
        node.mesh = static_cast<int>(jsonNode["mesh"].asNumber());
    }
    if (jsonNode.asObject().count("skin")) {
        node.skin = static_cast<int>(jsonNode["skin"].asNumber());
    }
    if (jsonNode.asObject().count("camera")) {
        node.camera = static_cast<int>(jsonNode["camera"].asNumber());
    }
    if (jsonNode.asObject().count("children")) {
        for (const auto& child : jsonNode["children"].asArray()) {
            node.children.push_back(static_cast<int>(child.asNumber()));
        }
    }
    if (jsonNode.asObject().count("translation")) {
        const JsonArray& t = jsonNode["translation"].asArray();
        node.translation = glm::vec3(t[0].asNumber(), t[1].asNumber(), t[2].asNumber());
    }
    if (jsonNode.asObject().count("rotation")) {
        const JsonArray& r = jsonNode["rotation"].asArray();
        node.rotation = glm::quat(r[3].asNumber(), r[0].asNumber(), r[1].asNumber(), r[2].asNumber()); // w, x, y, z
    }
    if (jsonNode.asObject().count("scale")) {
        const JsonArray& s = jsonNode["scale"].asArray();
        node.scale = glm::vec3(s[0].asNumber(), s[1].asNumber(), s[2].asNumber());
    }
    if (jsonNode.asObject().count("matrix")) {
        const JsonArray& m = jsonNode["matrix"].asArray();
        node.matrix.clear();
        for (const auto& val : m) {
            node.matrix.push_back(static_cast<float>(val.asNumber()));
        }
    }
}

void GltfParser::parseScene(const JsonValue& jsonScene, Scene& scene) {
    if (jsonScene.asObject().count("name")) {
        scene.name = jsonScene["name"].asString();
    }
    if (jsonScene.asObject().count("nodes")) {
        for (const auto& nodeIdx : jsonScene["nodes"].asArray()) {
            scene.nodes.push_back(static_cast<int>(nodeIdx.asNumber()));
        }
    }
}

void GltfParser::parseAnimationChannel(const JsonValue& jsonChannel, AnimationChannel& channel) {
    if (jsonChannel.asObject().count("sampler")) {
        channel.sampler = static_cast<int>(jsonChannel["sampler"].asNumber());
    }
    if (jsonChannel.asObject().count("target")) {
        const JsonValue& target = jsonChannel["target"];
        if (target.asObject().count("node")) {
            channel.target.node = static_cast<int>(target["node"].asNumber());
        }
        if (target.asObject().count("path")) {
            channel.target.path = target["path"].asString();
        }
    }
}

void GltfParser::parseAnimationSampler(const JsonValue& jsonSampler, AnimationSampler& sampler) {
    if (jsonSampler.asObject().count("input")) {
        sampler.input = static_cast<int>(jsonSampler["input"].asNumber());
    }
    if (jsonSampler.asObject().count("output")) {
        sampler.output = static_cast<int>(jsonSampler["output"].asNumber());
    }
    if (jsonSampler.asObject().count("interpolation")) {
        sampler.interpolation = jsonSampler["interpolation"].asString();
    }
}

void GltfParser::parseAnimation(const JsonValue& jsonAnimation, Animation& animation) {
    if (jsonAnimation.asObject().count("name")) {
        animation.name = jsonAnimation["name"].asString();
    }
    if (jsonAnimation.asObject().count("channels")) {
        for (const auto& jsonChannel : jsonAnimation["channels"].asArray()) {
            AnimationChannel channel;
            parseAnimationChannel(jsonChannel, channel);
            animation.channels.push_back(channel);
        }
    }
    if (jsonAnimation.asObject().count("samplers")) {
        for (const auto& jsonSampler : jsonAnimation["samplers"].asArray()) {
            AnimationSampler sampler;
            parseAnimationSampler(jsonSampler, sampler);
            animation.samplers.push_back(sampler);
        }
    }
}

void GltfParser::parseSkin(const JsonValue& jsonSkin, Skin& skin) {
    if (jsonSkin.asObject().count("name")) {
        skin.name = jsonSkin["name"].asString();
    }
    if (jsonSkin.asObject().count("inverseBindMatrices")) {
        skin.inverseBindMatrices = static_cast<int>(jsonSkin["inverseBindMatrices"].asNumber());
    }
    if (jsonSkin.asObject().count("skeleton")) {
        skin.skeleton = static_cast<int>(jsonSkin["skeleton"].asNumber());
    }
    if (jsonSkin.asObject().count("joints")) {
        for (const auto& joint : jsonSkin["joints"].asArray()) {
            skin.joints.push_back(static_cast<int>(joint.asNumber()));
        }
    }
}

// Base64 decoding implementation
static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

static inline bool is_base64(unsigned char c) {
  return (isalnum(c) || (c == '+') || (c == '/'));
}

std::vector<unsigned char> GltfParser::base64Decode(const std::string& encoded_string) {
    size_t in_len = encoded_string.length();
    size_t i = 0;
    size_t j = 0;
    int in_ = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::vector<unsigned char> ret;

    while (in_len-- && ( encoded_string[in_] != '=') && is_base64(encoded_string[in_])) {
        char_array_4[i++] = encoded_string[in_]; in_++;
        if (i == 4) {
            for (i = 0; i < 4; i++)
                char_array_4[i] = base64_chars.find(char_array_4[i]);

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++)
                ret.push_back(char_array_3[i]);
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 4; j++)
            char_array_4[j] = 0;

        for (j = 0; j < 4; j++)
            char_array_4[j] = base64_chars.find(char_array_4[j]);

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (j = 0; (j < i - 1); j++)
            ret.push_back(char_array_3[j]);
    }

    return ret;
}

GltfModel GltfParser::parse(const std::string& gltfJsonString, const std::string& baseDir) {
    GltfModel model;
    JsonValue json = JsonParser::parse(gltfJsonString);

    if (json.asObject().count("asset")) {
        parseAsset(json["asset"], model.asset);
    }

    if (json.asObject().count("scene")) {
        model.scene = static_cast<int>(json["scene"].asNumber());
    }

    if (json.asObject().count("scenes")) {
        for (const auto& jsonScene : json["scenes"].asArray()) {
            Scene scene;
            parseScene(jsonScene, scene);
            model.scenes.push_back(scene);
        }
    }

    if (json.asObject().count("nodes")) {
        for (const auto& jsonNode : json["nodes"].asArray()) {
            Node node;
            parseNode(jsonNode, node);
            model.nodes.push_back(node);
        }
    }

    if (json.asObject().count("meshes")) {
        for (const auto& jsonMesh : json["meshes"].asArray()) {
            Mesh mesh;
            parseMesh(jsonMesh, mesh);
            model.meshes.push_back(mesh);
        }
    }

    if (json.asObject().count("materials")) {
        for (const auto& jsonMaterial : json["materials"].asArray()) {
            Material material;
            parseMaterial(jsonMaterial, material);
            model.materials.push_back(material);
        }
    }

    if (json.asObject().count("buffers")) {
        for (const auto& jsonBuffer : json["buffers"].asArray()) {
            Buffer buffer;
            parseBuffer(jsonBuffer, buffer, baseDir);
            model.buffers.push_back(buffer);
        }
    }

    if (json.asObject().count("bufferViews")) {
        for (const auto& jsonBufferView : json["bufferViews"].asArray()) {
            BufferView bufferView;
            parseBufferView(jsonBufferView, bufferView);
            model.bufferViews.push_back(bufferView);
        }
    }

    if (json.asObject().count("accessors")) {
        for (const auto& jsonAccessor : json["accessors"].asArray()) {
            Accessor accessor;
            parseAccessor(jsonAccessor, accessor);
            model.accessors.push_back(accessor);
        }
    }

    if (json.asObject().count("images")) {
        for (const auto& jsonImage : json["images"].asArray()) {
            Image image;
            parseImage(jsonImage, image);
            model.images.push_back(image);
        }
    }

    if (json.asObject().count("samplers")) {
        for (const auto& jsonSampler : json["samplers"].asArray()) {
            Sampler sampler;
            parseSampler(jsonSampler, sampler);
            model.samplers.push_back(sampler);
        }
    }

    if (json.asObject().count("textures")) {
        for (const auto& jsonTexture : json["textures"].asArray()) {
            Texture texture;
            parseTexture(jsonTexture, texture);
            model.textures.push_back(texture);
        }
    }

    if (json.asObject().count("animations")) {
        for (const auto& jsonAnimation : json["animations"].asArray()) {
            Animation animation;
            parseAnimation(jsonAnimation, animation);
            model.animations.push_back(animation);
        }
    }

    /*if (json.asObject().count("skins")) {
        for (const auto& jsonSkin : json["skins"].asArray()) {
            Skin skin;
            parseSkin(jsonSkin, skin);
            model.skins.push_back(skin);
        }
    }*/

    return model;
}

} // namespace Gltf
