# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/test_google_cli/test1/CMakeLists.txt"
  "CMakeFiles/3.25.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.25.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.25.1/CMakeSystem.cmake"
  "/usr/local/lib/cmake/glfw3/glfw3Config.cmake"
  "/usr/local/lib/cmake/glfw3/glfw3ConfigVersion.cmake"
  "/usr/local/lib/cmake/glfw3/glfw3Targets-noconfig.cmake"
  "/usr/local/lib/cmake/glfw3/glfw3Targets.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.25/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.25/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.25/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.25/Modules/FindOpenGL.cmake"
  "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.25/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.25/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake/glm/glmConfig.cmake"
  "/usr/share/cmake/glm/glmConfigVersion.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/glad.dir/DependInfo.cmake"
  "CMakeFiles/Simple3DEngine.dir/DependInfo.cmake"
  )
