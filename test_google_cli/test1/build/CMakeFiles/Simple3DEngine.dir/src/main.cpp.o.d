CMakeFiles/Simple3DEngine.dir/src/main.cpp.o: \
 /home/<USER>/test_google_cli/test1/src/main.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/test_google_cli/test1/external/include/glad/glad.h \
 /home/<USER>/test_google_cli/test1/external/include/KHR/khrplatform.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/local/include/GLFW/glfw3.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h \
 /usr/include/c++/12/iostream \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h \
 /usr/include/c++/12/pstl/pstl_config.h /usr/include/c++/12/ostream \
 /usr/include/c++/12/ios /usr/include/c++/12/iosfwd \
 /usr/include/c++/12/bits/stringfwd.h \
 /usr/include/c++/12/bits/memoryfwd.h /usr/include/c++/12/bits/postypes.h \
 /usr/include/c++/12/cwchar /usr/include/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/c++/12/exception /usr/include/c++/12/bits/exception.h \
 /usr/include/c++/12/bits/exception_ptr.h \
 /usr/include/c++/12/bits/exception_defines.h \
 /usr/include/c++/12/bits/cxxabi_init_exception.h \
 /usr/include/c++/12/typeinfo /usr/include/c++/12/bits/hash_bytes.h \
 /usr/include/c++/12/new /usr/include/c++/12/bits/move.h \
 /usr/include/c++/12/type_traits \
 /usr/include/c++/12/bits/nested_exception.h \
 /usr/include/c++/12/bits/char_traits.h /usr/include/c++/12/cstdint \
 /usr/include/c++/12/bits/localefwd.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h \
 /usr/include/c++/12/clocale /usr/include/locale.h \
 /usr/include/x86_64-linux-gnu/bits/locale.h /usr/include/c++/12/cctype \
 /usr/include/ctype.h /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/c++/12/bits/ios_base.h /usr/include/c++/12/ext/atomicity.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/sched.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/timex.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
 /usr/include/x86_64-linux-gnu/bits/setjmp.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h \
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
 /usr/include/c++/12/bits/locale_classes.h /usr/include/c++/12/string \
 /usr/include/c++/12/bits/allocator.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h \
 /usr/include/c++/12/bits/new_allocator.h \
 /usr/include/c++/12/bits/functexcept.h \
 /usr/include/c++/12/bits/cpp_type_traits.h \
 /usr/include/c++/12/bits/ostream_insert.h \
 /usr/include/c++/12/bits/cxxabi_forced.h \
 /usr/include/c++/12/bits/stl_iterator_base_types.h \
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/12/bits/concept_check.h \
 /usr/include/c++/12/debug/assertions.h \
 /usr/include/c++/12/bits/stl_iterator.h \
 /usr/include/c++/12/ext/type_traits.h \
 /usr/include/c++/12/bits/ptr_traits.h \
 /usr/include/c++/12/bits/stl_function.h \
 /usr/include/c++/12/backward/binders.h \
 /usr/include/c++/12/ext/numeric_traits.h \
 /usr/include/c++/12/bits/stl_algobase.h \
 /usr/include/c++/12/bits/stl_pair.h /usr/include/c++/12/bits/utility.h \
 /usr/include/c++/12/debug/debug.h \
 /usr/include/c++/12/bits/predefined_ops.h \
 /usr/include/c++/12/bits/refwrap.h /usr/include/c++/12/bits/invoke.h \
 /usr/include/c++/12/bits/range_access.h \
 /usr/include/c++/12/initializer_list \
 /usr/include/c++/12/bits/basic_string.h \
 /usr/include/c++/12/ext/alloc_traits.h \
 /usr/include/c++/12/bits/alloc_traits.h \
 /usr/include/c++/12/bits/stl_construct.h /usr/include/c++/12/string_view \
 /usr/include/c++/12/bits/functional_hash.h \
 /usr/include/c++/12/bits/string_view.tcc \
 /usr/include/c++/12/ext/string_conversions.h /usr/include/c++/12/cstdlib \
 /usr/include/stdlib.h /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/sys/types.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/alloca.h /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/12/bits/std_abs.h /usr/include/c++/12/cstdio \
 /usr/include/stdio.h /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/c++/12/cerrno /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/12/bits/charconv.h \
 /usr/include/c++/12/bits/basic_string.tcc \
 /usr/include/c++/12/bits/locale_classes.tcc \
 /usr/include/c++/12/system_error \
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h \
 /usr/include/c++/12/stdexcept /usr/include/c++/12/streambuf \
 /usr/include/c++/12/bits/streambuf.tcc \
 /usr/include/c++/12/bits/basic_ios.h \
 /usr/include/c++/12/bits/locale_facets.h /usr/include/c++/12/cwctype \
 /usr/include/wctype.h /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h \
 /usr/include/c++/12/bits/streambuf_iterator.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h \
 /usr/include/c++/12/bits/locale_facets.tcc \
 /usr/include/c++/12/bits/basic_ios.tcc \
 /usr/include/c++/12/bits/ostream.tcc /usr/include/c++/12/istream \
 /usr/include/c++/12/bits/istream.tcc /usr/include/glm/glm.hpp \
 /usr/include/glm/detail/_fixes.hpp /usr/include/c++/12/cmath \
 /usr/include/math.h /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/c++/12/bits/specfun.h /usr/include/c++/12/limits \
 /usr/include/c++/12/tr1/gamma.tcc \
 /usr/include/c++/12/tr1/special_function_util.h \
 /usr/include/c++/12/tr1/bessel_function.tcc \
 /usr/include/c++/12/tr1/beta_function.tcc \
 /usr/include/c++/12/tr1/ell_integral.tcc \
 /usr/include/c++/12/tr1/exp_integral.tcc \
 /usr/include/c++/12/tr1/hypergeometric.tcc \
 /usr/include/c++/12/tr1/legendre_function.tcc \
 /usr/include/c++/12/tr1/modified_bessel_func.tcc \
 /usr/include/c++/12/tr1/poly_hermite.tcc \
 /usr/include/c++/12/tr1/poly_laguerre.tcc \
 /usr/include/c++/12/tr1/riemann_zeta.tcc \
 /usr/include/glm/detail/setup.hpp /usr/include/c++/12/cassert \
 /usr/include/assert.h /usr/include/c++/12/cstddef \
 /usr/include/glm/simd/platform.h /usr/include/c++/12/climits \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h /usr/include/c++/12/cfloat \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/float.h \
 /usr/include/glm/fwd.hpp /usr/include/glm/detail/qualifier.hpp \
 /usr/include/glm/detail/setup.hpp /usr/include/glm/vec2.hpp \
 /usr/include/glm/ext/vector_bool2.hpp \
 /usr/include/glm/detail/type_vec2.hpp \
 /usr/include/glm/detail/type_vec2.inl \
 /usr/include/glm/detail/compute_vector_relational.hpp \
 /usr/include/glm/ext/vector_bool2_precision.hpp \
 /usr/include/glm/ext/vector_float2.hpp \
 /usr/include/glm/ext/vector_float2_precision.hpp \
 /usr/include/glm/ext/vector_double2.hpp \
 /usr/include/glm/ext/vector_double2_precision.hpp \
 /usr/include/glm/ext/vector_int2.hpp \
 /usr/include/glm/ext/vector_int2_sized.hpp \
 /usr/include/glm/ext/scalar_int_sized.hpp \
 /usr/include/glm/detail/setup.hpp /usr/include/glm/ext/vector_uint2.hpp \
 /usr/include/glm/ext/vector_uint2_sized.hpp \
 /usr/include/glm/ext/scalar_uint_sized.hpp /usr/include/glm/vec3.hpp \
 /usr/include/glm/ext/vector_bool3.hpp \
 /usr/include/glm/detail/type_vec3.hpp \
 /usr/include/glm/detail/type_vec3.inl \
 /usr/include/glm/ext/vector_bool3_precision.hpp \
 /usr/include/glm/ext/vector_float3.hpp \
 /usr/include/glm/ext/vector_float3_precision.hpp \
 /usr/include/glm/ext/vector_double3.hpp \
 /usr/include/glm/ext/vector_double3_precision.hpp \
 /usr/include/glm/ext/vector_int3.hpp \
 /usr/include/glm/ext/vector_int3_sized.hpp \
 /usr/include/glm/ext/vector_uint3.hpp \
 /usr/include/glm/ext/vector_uint3_sized.hpp /usr/include/glm/vec4.hpp \
 /usr/include/glm/ext/vector_bool4.hpp \
 /usr/include/glm/detail/type_vec4.hpp \
 /usr/include/glm/detail/type_vec4.inl \
 /usr/include/glm/ext/vector_bool4_precision.hpp \
 /usr/include/glm/ext/vector_float4.hpp \
 /usr/include/glm/ext/vector_float4_precision.hpp \
 /usr/include/glm/ext/vector_double4.hpp \
 /usr/include/glm/ext/vector_double4_precision.hpp \
 /usr/include/glm/ext/vector_int4.hpp \
 /usr/include/glm/ext/vector_int4_sized.hpp \
 /usr/include/glm/ext/vector_uint4.hpp \
 /usr/include/glm/ext/vector_uint4_sized.hpp /usr/include/glm/mat2x2.hpp \
 /usr/include/glm/ext/matrix_double2x2.hpp \
 /usr/include/glm/detail/type_mat2x2.hpp \
 /usr/include/glm/detail/type_mat2x2.inl /usr/include/glm/matrix.hpp \
 /usr/include/glm/mat2x3.hpp /usr/include/glm/ext/matrix_double2x3.hpp \
 /usr/include/glm/detail/type_mat2x3.hpp \
 /usr/include/glm/detail/type_mat2x3.inl \
 /usr/include/glm/ext/matrix_double2x3_precision.hpp \
 /usr/include/glm/ext/matrix_float2x3.hpp \
 /usr/include/glm/ext/matrix_float2x3_precision.hpp \
 /usr/include/glm/mat2x4.hpp /usr/include/glm/ext/matrix_double2x4.hpp \
 /usr/include/glm/detail/type_mat2x4.hpp \
 /usr/include/glm/detail/type_mat2x4.inl \
 /usr/include/glm/ext/matrix_double2x4_precision.hpp \
 /usr/include/glm/ext/matrix_float2x4.hpp \
 /usr/include/glm/ext/matrix_float2x4_precision.hpp \
 /usr/include/glm/mat3x2.hpp /usr/include/glm/ext/matrix_double3x2.hpp \
 /usr/include/glm/detail/type_mat3x2.hpp \
 /usr/include/glm/detail/type_mat3x2.inl \
 /usr/include/glm/ext/matrix_double3x2_precision.hpp \
 /usr/include/glm/ext/matrix_float3x2.hpp \
 /usr/include/glm/ext/matrix_float3x2_precision.hpp \
 /usr/include/glm/mat3x3.hpp /usr/include/glm/ext/matrix_double3x3.hpp \
 /usr/include/glm/detail/type_mat3x3.hpp \
 /usr/include/glm/detail/type_mat3x3.inl \
 /usr/include/glm/ext/matrix_double3x3_precision.hpp \
 /usr/include/glm/ext/matrix_float3x3.hpp \
 /usr/include/glm/ext/matrix_float3x3_precision.hpp \
 /usr/include/glm/mat3x4.hpp /usr/include/glm/ext/matrix_double3x4.hpp \
 /usr/include/glm/detail/type_mat3x4.hpp \
 /usr/include/glm/detail/type_mat3x4.inl \
 /usr/include/glm/ext/matrix_double3x4_precision.hpp \
 /usr/include/glm/ext/matrix_float3x4.hpp \
 /usr/include/glm/ext/matrix_float3x4_precision.hpp \
 /usr/include/glm/mat4x2.hpp /usr/include/glm/ext/matrix_double4x2.hpp \
 /usr/include/glm/detail/type_mat4x2.hpp \
 /usr/include/glm/detail/type_mat4x2.inl \
 /usr/include/glm/ext/matrix_double4x2_precision.hpp \
 /usr/include/glm/ext/matrix_float4x2.hpp \
 /usr/include/glm/ext/matrix_float4x2_precision.hpp \
 /usr/include/glm/mat4x3.hpp /usr/include/glm/ext/matrix_double4x3.hpp \
 /usr/include/glm/detail/type_mat4x3.hpp \
 /usr/include/glm/detail/type_mat4x3.inl \
 /usr/include/glm/ext/matrix_double4x3_precision.hpp \
 /usr/include/glm/ext/matrix_float4x3.hpp \
 /usr/include/glm/ext/matrix_float4x3_precision.hpp \
 /usr/include/glm/mat4x4.hpp /usr/include/glm/ext/matrix_double4x4.hpp \
 /usr/include/glm/detail/type_mat4x4.hpp \
 /usr/include/glm/detail/type_mat4x4.inl \
 /usr/include/glm/ext/matrix_double4x4_precision.hpp \
 /usr/include/glm/ext/matrix_float4x4.hpp \
 /usr/include/glm/ext/matrix_float4x4_precision.hpp \
 /usr/include/glm/detail/func_matrix.inl /usr/include/glm/geometric.hpp \
 /usr/include/glm/detail/func_geometric.inl \
 /usr/include/glm/exponential.hpp /usr/include/glm/detail/type_vec1.hpp \
 /usr/include/glm/detail/type_vec1.inl \
 /usr/include/glm/detail/func_exponential.inl \
 /usr/include/glm/vector_relational.hpp \
 /usr/include/glm/detail/func_vector_relational.inl \
 /usr/include/glm/detail/_vectorize.hpp /usr/include/glm/common.hpp \
 /usr/include/glm/detail/func_common.inl \
 /usr/include/glm/detail/compute_common.hpp \
 /usr/include/glm/ext/matrix_double2x2_precision.hpp \
 /usr/include/glm/ext/matrix_float2x2.hpp \
 /usr/include/glm/ext/matrix_float2x2_precision.hpp \
 /usr/include/glm/trigonometric.hpp \
 /usr/include/glm/detail/func_trigonometric.inl \
 /usr/include/glm/packing.hpp /usr/include/glm/detail/func_packing.inl \
 /usr/include/glm/detail/type_half.hpp \
 /usr/include/glm/detail/type_half.inl /usr/include/glm/integer.hpp \
 /usr/include/glm/detail/func_integer.inl \
 /usr/include/glm/gtc/matrix_transform.hpp \
 /usr/include/glm/ext/matrix_projection.hpp \
 /usr/include/glm/gtc/constants.hpp \
 /usr/include/glm/ext/scalar_constants.hpp \
 /usr/include/glm/ext/scalar_constants.inl \
 /usr/include/glm/gtc/constants.inl \
 /usr/include/glm/ext/matrix_projection.inl \
 /usr/include/glm/ext/matrix_clip_space.hpp \
 /usr/include/glm/ext/matrix_clip_space.inl \
 /usr/include/glm/ext/matrix_transform.hpp \
 /usr/include/glm/ext/matrix_transform.inl \
 /usr/include/glm/gtc/matrix_transform.inl \
 /usr/include/glm/gtc/type_ptr.hpp /usr/include/glm/gtc/quaternion.hpp \
 /usr/include/glm/ext/vector_relational.hpp \
 /usr/include/glm/ext/vector_relational.inl \
 /usr/include/glm/detail/type_float.hpp \
 /usr/include/glm/ext/quaternion_common.hpp \
 /usr/include/glm/ext/quaternion_geometric.hpp \
 /usr/include/glm/ext/quaternion_geometric.inl \
 /usr/include/glm/ext/quaternion_common.inl \
 /usr/include/glm/ext/quaternion_float.hpp \
 /usr/include/glm/detail/type_quat.hpp \
 /usr/include/glm/ext/quaternion_relational.hpp \
 /usr/include/glm/ext/quaternion_relational.inl \
 /usr/include/glm/detail/type_quat.inl \
 /usr/include/glm/ext/quaternion_float_precision.hpp \
 /usr/include/glm/ext/quaternion_double.hpp \
 /usr/include/glm/ext/quaternion_double_precision.hpp \
 /usr/include/glm/ext/quaternion_trigonometric.hpp \
 /usr/include/glm/ext/quaternion_trigonometric.inl \
 /usr/include/glm/ext/quaternion_transform.hpp \
 /usr/include/glm/ext/quaternion_transform.inl \
 /usr/include/glm/gtc/quaternion.inl /usr/include/glm/gtc/epsilon.hpp \
 /usr/include/glm/detail/setup.hpp /usr/include/glm/gtc/epsilon.inl \
 /usr/include/glm/gtc/vec1.hpp /usr/include/glm/ext/vector_bool1.hpp \
 /usr/include/glm/ext/vector_bool1_precision.hpp \
 /usr/include/glm/ext/vector_float1.hpp \
 /usr/include/glm/ext/vector_float1_precision.hpp \
 /usr/include/glm/ext/vector_double1.hpp \
 /usr/include/glm/ext/vector_double1_precision.hpp \
 /usr/include/glm/ext/vector_int1.hpp \
 /usr/include/glm/ext/vector_int1_sized.hpp \
 /usr/include/glm/ext/vector_uint1.hpp \
 /usr/include/glm/ext/vector_uint1_sized.hpp /usr/include/c++/12/cstring \
 /usr/include/string.h /usr/include/strings.h \
 /usr/include/glm/gtc/type_ptr.inl \
 /home/<USER>/test_google_cli/test1/include/shader.h \
 /usr/include/c++/12/fstream /usr/include/c++/12/bits/codecvt.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/basic_file.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++io.h \
 /usr/include/c++/12/bits/fstream.tcc /usr/include/c++/12/sstream \
 /usr/include/c++/12/bits/sstream.tcc /usr/include/c++/12/vector \
 /usr/include/c++/12/bits/stl_uninitialized.h \
 /usr/include/c++/12/bits/stl_vector.h \
 /usr/include/c++/12/bits/stl_bvector.h \
 /usr/include/c++/12/bits/vector.tcc \
 /home/<USER>/test_google_cli/test1/include/camera.h \
 /home/<USER>/test_google_cli/test1/include/mesh.h \
 /home/<USER>/test_google_cli/test1/gltf_parser/include/gltf.h \
 /usr/include/c++/12/map /usr/include/c++/12/bits/stl_tree.h \
 /usr/include/c++/12/ext/aligned_buffer.h \
 /usr/include/c++/12/bits/node_handle.h \
 /usr/include/c++/12/bits/stl_map.h /usr/include/c++/12/tuple \
 /usr/include/c++/12/bits/uses_allocator.h \
 /usr/include/c++/12/bits/stl_multimap.h \
 /usr/include/c++/12/bits/erase_if.h \
 /home/<USER>/test_google_cli/test1/json_parser/include/json.h \
 /usr/include/c++/12/variant \
 /usr/include/c++/12/bits/enable_special_members.h \
 /usr/include/c++/12/bits/parse_numbers.h \
 /home/<USER>/test_google_cli/test1/include/animation.h \
 /home/<USER>/test_google_cli/test1/utils/include/logger.h \
 /usr/include/c++/12/mutex /usr/include/c++/12/bits/chrono.h \
 /usr/include/c++/12/ratio /usr/include/c++/12/ctime \
 /usr/include/c++/12/bits/std_mutex.h \
 /usr/include/c++/12/bits/unique_lock.h /usr/include/c++/12/chrono \
 /usr/include/c++/12/iomanip /usr/include/c++/12/locale \
 /usr/include/c++/12/bits/locale_facets_nonio.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h \
 /usr/include/libintl.h /usr/include/c++/12/bits/locale_facets_nonio.tcc \
 /usr/include/c++/12/bits/locale_conv.h \
 /usr/include/c++/12/bits/quoted_string.h /usr/include/c++/12/memory \
 /usr/include/c++/12/bits/stl_tempbuf.h \
 /usr/include/c++/12/bits/stl_raw_storage_iter.h \
 /usr/include/c++/12/bits/align.h /usr/include/c++/12/bit \
 /usr/include/c++/12/bits/unique_ptr.h \
 /usr/include/c++/12/bits/shared_ptr.h \
 /usr/include/c++/12/bits/shared_ptr_base.h \
 /usr/include/c++/12/bits/allocated_ptr.h \
 /usr/include/c++/12/ext/concurrence.h \
 /usr/include/c++/12/bits/shared_ptr_atomic.h \
 /usr/include/c++/12/bits/atomic_base.h \
 /usr/include/c++/12/bits/atomic_lockfree_defines.h \
 /usr/include/c++/12/backward/auto_ptr.h \
 /usr/include/c++/12/pstl/glue_memory_defs.h \
 /usr/include/c++/12/pstl/execution_defs.h \
 /home/<USER>/test_google_cli/test1/utils/include/error_handler.h \
 /usr/include/c++/12/functional /usr/include/c++/12/bits/std_function.h \
 /usr/include/c++/12/unordered_map /usr/include/c++/12/bits/hashtable.h \
 /usr/include/c++/12/bits/hashtable_policy.h \
 /usr/include/c++/12/bits/unordered_map.h /usr/include/c++/12/array \
 /usr/include/c++/12/compare /usr/include/c++/12/bits/stl_algo.h \
 /usr/include/c++/12/bits/algorithmfwd.h \
 /usr/include/c++/12/bits/stl_heap.h \
 /usr/include/c++/12/bits/uniform_int_dist.h \
 /home/<USER>/test_google_cli/test1/utils/include/logger.h
