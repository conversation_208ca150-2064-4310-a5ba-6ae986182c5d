# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test1/build

# Include any dependencies generated for this target.
include CMakeFiles/Simple3DEngine.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/Simple3DEngine.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/Simple3DEngine.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/Simple3DEngine.dir/flags.make

CMakeFiles/Simple3DEngine.dir/src/main.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/src/main.cpp.o: /home/<USER>/test_google_cli/test1/src/main.cpp
CMakeFiles/Simple3DEngine.dir/src/main.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/Simple3DEngine.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/src/main.cpp.o -MF CMakeFiles/Simple3DEngine.dir/src/main.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/src/main.cpp.o -c /home/<USER>/test_google_cli/test1/src/main.cpp

CMakeFiles/Simple3DEngine.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/src/main.cpp > CMakeFiles/Simple3DEngine.dir/src/main.cpp.i

CMakeFiles/Simple3DEngine.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/src/main.cpp -o CMakeFiles/Simple3DEngine.dir/src/main.cpp.s

CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o: /home/<USER>/test_google_cli/test1/src/shader.cpp
CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o -MF CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o -c /home/<USER>/test_google_cli/test1/src/shader.cpp

CMakeFiles/Simple3DEngine.dir/src/shader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/src/shader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/src/shader.cpp > CMakeFiles/Simple3DEngine.dir/src/shader.cpp.i

CMakeFiles/Simple3DEngine.dir/src/shader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/src/shader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/src/shader.cpp -o CMakeFiles/Simple3DEngine.dir/src/shader.cpp.s

CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o: /home/<USER>/test_google_cli/test1/src/camera.cpp
CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o -MF CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o -c /home/<USER>/test_google_cli/test1/src/camera.cpp

CMakeFiles/Simple3DEngine.dir/src/camera.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/src/camera.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/src/camera.cpp > CMakeFiles/Simple3DEngine.dir/src/camera.cpp.i

CMakeFiles/Simple3DEngine.dir/src/camera.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/src/camera.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/src/camera.cpp -o CMakeFiles/Simple3DEngine.dir/src/camera.cpp.s

CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o: /home/<USER>/test_google_cli/test1/src/mesh.cpp
CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o -MF CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o -c /home/<USER>/test_google_cli/test1/src/mesh.cpp

CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/src/mesh.cpp > CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.i

CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/src/mesh.cpp -o CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.s

CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o: /home/<USER>/test_google_cli/test1/src/animation.cpp
CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o -MF CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o -c /home/<USER>/test_google_cli/test1/src/animation.cpp

CMakeFiles/Simple3DEngine.dir/src/animation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/src/animation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/src/animation.cpp > CMakeFiles/Simple3DEngine.dir/src/animation.cpp.i

CMakeFiles/Simple3DEngine.dir/src/animation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/src/animation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/src/animation.cpp -o CMakeFiles/Simple3DEngine.dir/src/animation.cpp.s

CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o: /home/<USER>/test_google_cli/test1/json_parser/src/json.cpp
CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o -MF CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o -c /home/<USER>/test_google_cli/test1/json_parser/src/json.cpp

CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/json_parser/src/json.cpp > CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.i

CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/json_parser/src/json.cpp -o CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.s

CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o: /home/<USER>/test_google_cli/test1/gltf_parser/src/gltf.cpp
CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o -MF CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o -c /home/<USER>/test_google_cli/test1/gltf_parser/src/gltf.cpp

CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/gltf_parser/src/gltf.cpp > CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.i

CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/gltf_parser/src/gltf.cpp -o CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.s

CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o: /home/<USER>/test_google_cli/test1/utils/src/base_encoding.cpp
CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o -MF CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o -c /home/<USER>/test_google_cli/test1/utils/src/base_encoding.cpp

CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/utils/src/base_encoding.cpp > CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.i

CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/utils/src/base_encoding.cpp -o CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.s

CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o: /home/<USER>/test_google_cli/test1/utils/src/directory_utils.cpp
CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o -MF CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o -c /home/<USER>/test_google_cli/test1/utils/src/directory_utils.cpp

CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/utils/src/directory_utils.cpp > CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.i

CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/utils/src/directory_utils.cpp -o CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.s

CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o: /home/<USER>/test_google_cli/test1/utils/src/logger.cpp
CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o -MF CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o -c /home/<USER>/test_google_cli/test1/utils/src/logger.cpp

CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/utils/src/logger.cpp > CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.i

CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/utils/src/logger.cpp -o CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.s

CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o: CMakeFiles/Simple3DEngine.dir/flags.make
CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o: /home/<USER>/test_google_cli/test1/utils/src/error_handler.cpp
CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o: CMakeFiles/Simple3DEngine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o -MF CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o.d -o CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o -c /home/<USER>/test_google_cli/test1/utils/src/error_handler.cpp

CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/test1/utils/src/error_handler.cpp > CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.i

CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/test1/utils/src/error_handler.cpp -o CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.s

# Object files for target Simple3DEngine
Simple3DEngine_OBJECTS = \
"CMakeFiles/Simple3DEngine.dir/src/main.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o" \
"CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o"

# External object files for target Simple3DEngine
Simple3DEngine_EXTERNAL_OBJECTS =

Simple3DEngine: CMakeFiles/Simple3DEngine.dir/src/main.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/build.make
Simple3DEngine: libglad.a
Simple3DEngine: /usr/local/lib/libglfw3.a
Simple3DEngine: /usr/lib/x86_64-linux-gnu/libGL.so
Simple3DEngine: /usr/lib/x86_64-linux-gnu/librt.a
Simple3DEngine: /usr/lib/x86_64-linux-gnu/libm.so
Simple3DEngine: CMakeFiles/Simple3DEngine.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX executable Simple3DEngine"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Simple3DEngine.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/Simple3DEngine.dir/build: Simple3DEngine
.PHONY : CMakeFiles/Simple3DEngine.dir/build

CMakeFiles/Simple3DEngine.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/Simple3DEngine.dir/cmake_clean.cmake
.PHONY : CMakeFiles/Simple3DEngine.dir/clean

CMakeFiles/Simple3DEngine.dir/depend:
	cd /home/<USER>/test_google_cli/test1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/test1 /home/<USER>/test_google_cli/test1 /home/<USER>/test_google_cli/test1/build /home/<USER>/test_google_cli/test1/build /home/<USER>/test_google_cli/test1/build/CMakeFiles/Simple3DEngine.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/Simple3DEngine.dir/depend

