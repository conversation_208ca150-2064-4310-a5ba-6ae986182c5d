/usr/bin/c++ CMakeFiles/Simple3DEngine.dir/src/main.cpp.o CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o CMakeFiles/Simple3DEngine.dir/src/animation.cpp.o CMakeFiles/Simple3DEngine.dir/src/model_controller.cpp.o CMakeFiles/Simple3DEngine.dir/src/application.cpp.o CMakeFiles/Simple3DEngine.dir/src/texture.cpp.o CMakeFiles/Simple3DEngine.dir/src/model.cpp.o CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o -o Simple3DEngine  libglad.a /usr/local/lib/libglfw3.a /usr/lib/x86_64-linux-gnu/libGL.so -ldl /usr/lib/x86_64-linux-gnu/librt.a -lm -ldl 
