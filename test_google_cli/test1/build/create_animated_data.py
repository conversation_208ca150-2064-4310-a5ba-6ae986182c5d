#!/usr/bin/env python3
"""
Create binary data for animated glTF character
"""
import struct
import math

def create_animated_character_data():
    data = bytearray()

    # Vertex positions (4 vertices forming a simple quad)
    # Make sure these are reasonable values
    positions = [
        -0.5, -0.5, 0.0,  # Bottom left
         0.5, -0.5, 0.0,  # Bottom right
         0.5,  0.5, 0.0,  # Top right
        -0.5,  0.5, 0.0   # Top left
    ]

    # Pack positions as floats
    for pos in positions:
        data.extend(struct.pack('<f', pos))
    
    # Normals (all pointing forward)
    normals = [
        0.0, 0.0, 1.0,  # Vertex 0
        0.0, 0.0, 1.0,  # Vertex 1
        0.0, 0.0, 1.0,  # Vertex 2
        0.0, 0.0, 1.0   # Vertex 3
    ]
    
    # Pack normals as floats
    for normal in normals:
        data.extend(struct.pack('<f', normal))
    
    # Joint indices (bone indices) - 4 bytes per vertex
    # Bottom vertices influenced by bone 0, top vertices by bone 1
    joint_indices = [
        0, 0, 0, 0,  # Vertex 0: only bone 0
        0, 0, 0, 0,  # Vertex 1: only bone 0
        1, 1, 0, 0,  # Vertex 2: mainly bone 1, some bone 0
        1, 1, 0, 0   # Vertex 3: mainly bone 1, some bone 0
    ]
    
    # Pack joint indices as unsigned bytes
    for joint in joint_indices:
        data.extend(struct.pack('<B', joint))
    
    # Joint weights (bone weights) - 4 floats per vertex
    joint_weights = [
        1.0, 0.0, 0.0, 0.0,  # Vertex 0: 100% bone 0
        1.0, 0.0, 0.0, 0.0,  # Vertex 1: 100% bone 0
        0.3, 0.7, 0.0, 0.0,  # Vertex 2: 30% bone 0, 70% bone 1
        0.3, 0.7, 0.0, 0.0   # Vertex 3: 30% bone 0, 70% bone 1
    ]
    
    # Pack joint weights as floats
    for weight in joint_weights:
        data.extend(struct.pack('<f', weight))
    
    # Indices for triangles (2 triangles forming a quad)
    indices = [0, 1, 2, 0, 2, 3]
    
    # Pack indices as unsigned shorts
    for index in indices:
        data.extend(struct.pack('<H', index))
    
    # Inverse bind matrices (2 bones, 4x4 matrices each)
    # Use IDENTITY matrices for simplicity - no bone offset
    # glTF matrices are stored in COLUMN-MAJOR order
    inverse_bind_matrices = [
        # Bone 1 (joint index 0) inverse bind matrix - identity
        1.0, 0.0, 0.0, 0.0,  # Column 0
        0.0, 1.0, 0.0, 0.0,  # Column 1
        0.0, 0.0, 1.0, 0.0,  # Column 2
        0.0, 0.0, 0.0, 1.0,  # Column 3

        # Bone 2 (joint index 1) inverse bind matrix - identity
        1.0, 0.0, 0.0, 0.0,  # Column 0
        0.0, 1.0, 0.0, 0.0,  # Column 1
        0.0, 0.0, 1.0, 0.0,  # Column 2
        0.0, 0.0, 0.0, 1.0   # Column 3
    ]
    
    # Pack inverse bind matrices as floats
    for matrix_value in inverse_bind_matrices:
        data.extend(struct.pack('<f', matrix_value))
    
    # Animation time keyframes (3 keyframes: 0, 1, 2 seconds)
    # CRITICAL: These must be valid positive time values
    time_keyframes = [0.0, 1.0, 2.0]
    
    # Pack time keyframes as floats
    for time in time_keyframes:
        data.extend(struct.pack('<f', time))
    
    # Animation rotation keyframes (quaternions for bone 2)
    # Rotate from 0 to 90 degrees and back
    rotation_keyframes = [
        # Keyframe 0: no rotation
        0.0, 0.0, 0.0, 1.0,
        # Keyframe 1: 45 degree rotation around Z axis
        0.0, 0.0, math.sin(math.pi/8), math.cos(math.pi/8),
        # Keyframe 2: back to no rotation
        0.0, 0.0, 0.0, 1.0
    ]
    
    # Pack rotation keyframes as floats
    for rotation in rotation_keyframes:
        data.extend(struct.pack('<f', rotation))
    
    return data

if __name__ == "__main__":
    data = create_animated_character_data()
    
    with open("animated_character.bin", "wb") as f:
        f.write(data)
    
    print(f"Created animated_character.bin with {len(data)} bytes")
    print("Data layout:")
    print("  Positions: 0-47 (48 bytes)")
    print("  Normals: 48-95 (48 bytes)")
    print("  Joint indices: 96-111 (16 bytes)")
    print("  Joint weights: 112-175 (64 bytes)")
    print("  Indices: 176-187 (12 bytes)")
    print("  Inverse bind matrices: 188-315 (128 bytes)")
    print("  Time keyframes: 316-327 (12 bytes)")
    print("  Rotation keyframes: 328-375 (48 bytes)")
