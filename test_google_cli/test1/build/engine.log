[2025-06-27 20:03:07] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:03:07] [INFO] GLFW window created successfully.
[2025-06-27 20:03:07] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:03:07] [INFO] GLSL Version: 4.60
[2025-06-27 20:03:07] [ERROR] (/home/<USER>/test_google_cli/test1/src/shader.cpp:32) Error: Shader file not successfully read: basic_ios::clear: iostream error
[2025-06-27 20:03:07] [FATAL] (/home/<USER>/test_google_cli/test1/src/shader.cpp:32) Fatal Error: Shader file not successfully read: basic_ios::clear: iostream error
[2025-06-27 20:05:04] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:05:04] [INFO] GLFW window created successfully.
[2025-06-27 20:05:04] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:05:04] [INFO] GLSL Version: 4.60
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:05:04] [INFO] Shader program compiled.
[2025-06-27 20:05:04] [INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:140) glTF file parsed successfully.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:44) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:231) Mesh loaded from glTF.
[2025-06-27 20:05:10] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 20:13:25] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:13:25] [INFO] GLFW window created successfully.
[2025-06-27 20:13:25] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:13:25] [INFO] GLSL Version: 4.60
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:13:26] [INFO] Shader program compiled.
[2025-06-27 20:13:26] [INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:141) glTF file parsed successfully.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:50) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:268) Mesh loaded from glTF.
[2025-06-27 20:13:26] [INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:13:29] [INFO] Terminating GLFW and cleaning up resources.
