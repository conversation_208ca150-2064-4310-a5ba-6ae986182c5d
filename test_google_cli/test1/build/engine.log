[2025-06-27 20:03:07] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:03:07] [INFO] GLFW window created successfully.
[2025-06-27 20:03:07] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:03:07] [INFO] GLSL Version: 4.60
[2025-06-27 20:03:07] [ERROR] (/home/<USER>/test_google_cli/test1/src/shader.cpp:32) Error: Shader file not successfully read: basic_ios::clear: iostream error
[2025-06-27 20:03:07] [FATAL] (/home/<USER>/test_google_cli/test1/src/shader.cpp:32) Fatal Error: Shader file not successfully read: basic_ios::clear: iostream error
[2025-06-27 20:05:04] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:05:04] [INFO] GLFW window created successfully.
[2025-06-27 20:05:04] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:05:04] [INFO] GLSL Version: 4.60
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:05:04] [INFO] Shader program compiled.
[2025-06-27 20:05:04] [INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:140) glTF file parsed successfully.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:44) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:05:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:231) Mesh loaded from glTF.
[2025-06-27 20:05:10] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 20:13:25] [INFO] Starting Simple 3D Engine...
[2025-06-27 20:13:25] [INFO] GLFW window created successfully.
[2025-06-27 20:13:25] [INFO] OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:13:25] [INFO] GLSL Version: 4.60
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:13:26] [INFO] Shader program compiled.
[2025-06-27 20:13:26] [INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:141) glTF file parsed successfully.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:50) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:13:26] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:268) Mesh loaded from glTF.
[2025-06-27 20:13:26] [INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:13:29] [INFO] Terminating GLFW and cleaning up resources.

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:23:40.069
================================================================================

[2025-06-27 20:23:40.069] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:23:40.069] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:23:40.326] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:23:40.331] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:23:40.331] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:23:40.357] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:23:40.357] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:23:40.357] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:23:40.357] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:23:40.357] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:23:40.358] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:23:40.358] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:23:40.358] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:23:40.358] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:23:40.358] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:23:40.359] [ INFO] (mesh.cpp:50) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:23:40.359] [ INFO] (mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:23:40.359] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:23:40.359] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:23:40.359] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:23:40.359] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:23:40.359] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:23:40.360] [TRACE] (main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:23:40.360] [TRACE] (main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:23:40.360] [ INFO] (main.cpp:338) Starting main rendering loop...
[2025-06-27 20:23:40.360] [TRACE] (main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:23:40.360] [TRACE] (main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:23:40.360] [TRACE] (main.cpp:424) Model Matrix:
  [   1.994,    0.000,   -0.157,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.157,    0.000,    1.994,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:23:40.360] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.360] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.360] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.360] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.362] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.362] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.362] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.362] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.387] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.388] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.388] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.388] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.402] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.403] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.404] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.406] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.410] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.417] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.417] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.418] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.428] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.433] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.433] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.434] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.444] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.445] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.445] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.446] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.460] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.460] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.460] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.460] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.476] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.476] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.476] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.476] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.493] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.494] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.494] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.494] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.510] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.510] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.510] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.511] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.528] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.528] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.528] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.529] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.543] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.543] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.543] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.544] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.559] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.559] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.559] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.559] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.576] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.577] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.577] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.577] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.593] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.593] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.593] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.593] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.609] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.609] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.609] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.610] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.626] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.627] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.627] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.627] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.643] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.643] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.643] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.643] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.672] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.672] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.672] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.672] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.686] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.686] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.686] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.687] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.704] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.704] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.704] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.704] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.722] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.722] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.722] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.722] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.737] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.737] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.737] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.738] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.756] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.756] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.756] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.756] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.771] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.771] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.771] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.771] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.787] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.787] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.787] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.788] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.803] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.803] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.803] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.803] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.822] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.823] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.823] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.823] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.840] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.840] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.840] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.840] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.856] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.857] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.857] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.857] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.872] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.872] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.872] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.873] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.888] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.889] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.889] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.889] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.905] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.905] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.906] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.906] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.922] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.924] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.924] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.925] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.939] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.939] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.939] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.940] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.956] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.956] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.956] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.956] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.971] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.972] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.972] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.972] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:40.988] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:40.988] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:40.988] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:40.988] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.005] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.005] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.005] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.006] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.021] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.022] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.022] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.022] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.037] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.037] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.038] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.038] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.054] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.054] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.054] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.055] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.070] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.071] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.071] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.071] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.087] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.088] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.088] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.088] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.104] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.105] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.105] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.105] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.120] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.121] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.121] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.121] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.137] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.137] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.138] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.138] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.154] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.154] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.154] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.154] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.170] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.171] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.171] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.171] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.187] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.187] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.187] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.188] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.204] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.204] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.204] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.204] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.220] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.220] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.220] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.221] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.237] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.237] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.237] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.238] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.254] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.254] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.254] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.254] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.270] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.270] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.271] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.271] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.287] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.288] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.288] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.288] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.304] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.304] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.304] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.304] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.320] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.321] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.321] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.321] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.337] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.337] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.337] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.337] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.355] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.356] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.356] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.356] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.371] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.372] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.372] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.372] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.388] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.388] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.388] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.388] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.405] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.405] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.405] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.405] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.422] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.422] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.422] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.422] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.438] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.438] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.438] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.438] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.455] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.455] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.455] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.455] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.471] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.471] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.471] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.471] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.488] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.488] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.488] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.488] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.504] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.505] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.505] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.505] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.521] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.521] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.521] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.521] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.538] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.538] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.538] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.538] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.554] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.555] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.555] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.555] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.571] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.571] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.571] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.571] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.588] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.588] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.588] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.588] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.604] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.605] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.605] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.605] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.621] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.621] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.622] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.622] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.638] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.638] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.638] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.638] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.654] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.655] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.655] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.655] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.671] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.671] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.671] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.671] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.688] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.688] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.688] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.688] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.704] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.705] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.705] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.705] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.722] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.722] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.723] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.723] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.738] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.738] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.739] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.739] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.755] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.755] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.755] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.755] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.771] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.771] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.771] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.771] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.788] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.788] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.788] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.788] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.804] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.805] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.805] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.805] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.821] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.821] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.821] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.821] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.838] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.838] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.838] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.838] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.854] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.855] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.855] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.855] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.871] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.871] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.871] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.871] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.888] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.888] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.888] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.888] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.904] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.904] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.905] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.905] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.921] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.921] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.921] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.921] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.938] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.938] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.938] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.938] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.954] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.955] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.955] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.955] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.971] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.971] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.971] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.971] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:41.988] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:41.988] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:41.988] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:41.988] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.005] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.005] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.005] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.005] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.021] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.022] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.022] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.022] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.037] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.037] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.037] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.037] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.054] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.054] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.054] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.054] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.070] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.070] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.070] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.070] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.087] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.087] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.087] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.087] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.103] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.104] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.104] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.104] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.121] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.121] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.121] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.121] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.137] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.137] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.137] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.137] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.154] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.154] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.155] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.155] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.170] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.170] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.170] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.171] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.187] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.187] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.187] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.187] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.203] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.204] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.204] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.204] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.220] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.220] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.220] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.220] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.237] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:42.237] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:42.237] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[2025-06-27 20:23:42.237] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:42.253] [ INFO] (main.cpp:439) Terminating GLFW and cleaning up resources...
[2025-06-27 20:23:42.266] [ INFO] (main.cpp:448) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:23:50.016
================================================================================

[2025-06-27 20:23:50.016] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:23:50.016] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:23:50.199] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:23:50.202] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:23:50.202] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:23:50.202] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:23:50.202] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:23:50.203] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:23:50.203] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:23:50.203] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:23:50.215] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:23:50.215] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:23:50.215] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:23:50.215] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:23:50.216] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:23:50.217] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:23:50.217] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:23:50.217] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:23:50.217] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:23:50.217] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:23:50.217] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:23:50.217] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:23:50.217] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:23:50.217] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:23:50.220] [ INFO] (mesh.cpp:50) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:23:50.220] [ INFO] (mesh.cpp:10) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:23:50.221] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:23:50.221] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:23:50.221] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:23:50.221] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:23:50.221] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:23:50.221] [TRACE] (main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:23:50.221] [TRACE] (main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:23:50.221] [ INFO] (main.cpp:338) Starting main rendering loop...
[2025-06-27 20:23:50.221] [TRACE] (main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:23:50.221] [TRACE] (main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:23:50.222] [TRACE] (main.cpp:424) Model Matrix:
  [   1.998,    0.000,   -0.098,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.098,    0.000,    1.998,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:23:50.222] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.222] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.222] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.222] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.224] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.225] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.225] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.225] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.260] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.260] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.260] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.260] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.267] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.267] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.267] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.267] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.296] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.297] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.297] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.297] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.313] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.313] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.313] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.313] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.329] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.330] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.330] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.330] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.346] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.346] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.346] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.346] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.364] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.364] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.365] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.365] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.380] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.381] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.381] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.381] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.398] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.398] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.398] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.398] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.415] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.415] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.415] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.415] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.431] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.431] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.431] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.431] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.447] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.448] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.448] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.448] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.464] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.464] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.464] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.464] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.481] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.481] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.481] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.482] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.498] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.498] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.498] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.498] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.515] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.515] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.515] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.515] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.531] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.532] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.532] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.532] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.553] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.554] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.554] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.554] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.568] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.568] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.568] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.568] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.583] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.583] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.583] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.584] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.600] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.600] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.600] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.600] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.616] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.617] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.617] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.617] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.635] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.635] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.635] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.635] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.652] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.652] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.652] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.652] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.667] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.667] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.667] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.668] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.683] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.683] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.683] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.683] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.700] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.701] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.701] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.701] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.720] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.721] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.721] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.721] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.736] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.737] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.737] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.737] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.752] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.752] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.752] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.752] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.768] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.769] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.769] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.769] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.785] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.786] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.786] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.786] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.802] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.802] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.802] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.803] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.819] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.819] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.819] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.819] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.836] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.836] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.836] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.836] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.852] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.852] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.852] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.853] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.868] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.868] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.868] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.868] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.885] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.885] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.885] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.885] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.901] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.902] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.902] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.902] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.918] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.918] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.918] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.919] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.935] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.935] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.935] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.935] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.952] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.952] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.952] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.952] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.968] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.968] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.968] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.969] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:50.985] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:50.986] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:50.986] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:50.987] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.002] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.002] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.002] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.002] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.030] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.030] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.030] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.030] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.040] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.040] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.041] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.041] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.066] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.067] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.067] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.067] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.079] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.080] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.080] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.080] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.096] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.096] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.096] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.096] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.112] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.112] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.112] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.112] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.129] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.129] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.129] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.129] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.145] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.145] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.146] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.146] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.162] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.162] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.162] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.163] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.178] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.179] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.179] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.179] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.195] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.195] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.195] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.196] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.212] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.212] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.212] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.212] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.228] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.229] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.229] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.229] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.245] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.245] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.245] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.246] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.262] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.262] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.262] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.262] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.278] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.279] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.279] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.279] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.295] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.296] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.296] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.296] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.312] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.312] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.313] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.313] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.328] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.329] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.329] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.329] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.349] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.349] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.349] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.350] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.366] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.366] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.366] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.367] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.382] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.383] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.383] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.383] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.403] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.403] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.403] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.403] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.419] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.419] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.419] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.419] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.435] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.435] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.435] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.435] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.451] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.452] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.452] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.452] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.468] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.469] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.469] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.469] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.485] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.485] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.485] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.485] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.501] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.502] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.502] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.502] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.518] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.518] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.518] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.518] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.535] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.535] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.535] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.535] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.551] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.552] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.552] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.552] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.568] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.569] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.569] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.569] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.585] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.585] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.585] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.585] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.601] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.601] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.602] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.602] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.618] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.618] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.618] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.618] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.635] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.635] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.635] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.635] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.651] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.652] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.652] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.652] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.667] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.668] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.668] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.668] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.684] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.684] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.684] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.684] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.702] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.702] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.702] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.702] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.720] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.722] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.722] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.723] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.735] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.735] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.735] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.736] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.751] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.752] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.752] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.752] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.768] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.768] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.768] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.768] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.784] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.785] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.785] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.785] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.801] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.801] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.802] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.802] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.818] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.818] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.818] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.819] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.834] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.835] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.835] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.835] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.851] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.851] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.851] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.851] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.868] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.868] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.868] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.868] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.884] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.885] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.885] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.885] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.901] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.902] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.902] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.902] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.918] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.918] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.918] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.918] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.935] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.935] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.935] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.935] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.951] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.951] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.951] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.951] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.968] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:23:51.968] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:23:51.968] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[2025-06-27 20:23:51.968] [TRACE] (mesh.cpp:17) Mesh drawn.
[2025-06-27 20:23:51.984] [ INFO] (main.cpp:439) Terminating GLFW and cleaning up resources...
[2025-06-27 20:23:51.996] [ INFO] (main.cpp:448) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:25:30.822
================================================================================

[2025-06-27 20:25:30.822] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:25:30.822] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:25:31.031] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:25:31.034] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:25:31.034] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:25:31.045] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:25:31.045] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:25:31.045] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:25:31.045] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:25:31.046] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:25:31.046] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:25:31.046] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:25:31.046] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:25:31.046] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:25:31.047] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:25:31.047] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:25:31.047] [TRACE] (mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[2025-06-27 20:25:31.047] [TRACE] (mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[2025-06-27 20:25:31.048] [ INFO] (mesh.cpp:76) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:25:31.048] [ INFO] (mesh.cpp:11) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:25:31.048] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:25:31.048] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:25:31.048] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:25:31.048] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:25:31.048] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:25:31.048] [TRACE] (main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:25:31.048] [TRACE] (main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:25:31.048] [ INFO] (main.cpp:338) Starting main rendering loop...
[2025-06-27 20:25:31.048] [TRACE] (main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:25:31.048] [TRACE] (main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:25:31.048] [TRACE] (main.cpp:424) Model Matrix:
  [   1.997,    0.000,   -0.116,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.116,    0.000,    1.997,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:25:31.048] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.049] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.049] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.049] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.049] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.050] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.050] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.050] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.050] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.050] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.070] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.070] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.070] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.071] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.071] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.072] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.072] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.072] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.072] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.073] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.100] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.101] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.101] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.101] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.101] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.116] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.116] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.117] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.117] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.117] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.133] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.133] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.133] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.133] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.133] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.149] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.149] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.149] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.150] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.150] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.167] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.167] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.167] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.167] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.167] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.184] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.184] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.184] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.184] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.184] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.203] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.203] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.203] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.203] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.204] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.218] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.218] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.218] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.218] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.218] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.235] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.235] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.235] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.235] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.236] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.251] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.252] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.254] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.254] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.255] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.270] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.271] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.271] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.271] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.271] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.288] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.289] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.289] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.289] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.289] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.299] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.299] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.300] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.301] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.302] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.318] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.318] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.318] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.318] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.318] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.333] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.333] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.333] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.333] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.334] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.351] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.352] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.352] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.352] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.352] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.371] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.371] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.371] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.372] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.372] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.386] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.387] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.387] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.387] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.387] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.402] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.402] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.403] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.403] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.404] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.415] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.416] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.416] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.416] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.417] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.432] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.432] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.432] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.433] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.434] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.448] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.448] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.448] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.448] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.448] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.463] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.463] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.463] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.463] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.464] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.479] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.479] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.479] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.479] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.480] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.496] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.496] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.496] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.496] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.496] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.513] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.513] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.513] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.513] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.513] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.529] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.529] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.529] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.529] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.530] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.546] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.546] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.546] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.546] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.546] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.562] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.563] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.563] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.563] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.563] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.580] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.580] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.580] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.580] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.581] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.596] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.596] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.597] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.597] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.597] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.612] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.613] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.613] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.613] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.613] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.629] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.629] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.629] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.630] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.630] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.649] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.649] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.649] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.649] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.649] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.665] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.666] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.666] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.666] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.666] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.684] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.684] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.684] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.684] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.684] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.701] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.701] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.701] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.701] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.701] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.717] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.717] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.717] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.717] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.717] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.738] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.738] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.738] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.738] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.738] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.753] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.754] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.758] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.758] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.758] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.769] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.770] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.770] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.770] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.770] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.786] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.787] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.787] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.787] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.787] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.803] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.803] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.803] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.803] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.803] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.820] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.821] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.821] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.821] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.821] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.837] [TRACE] (main.cpp:482) Mouse input initialized at (1.070312, 2.675781)
[2025-06-27 20:25:31.837] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.837] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.837] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.837] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.837] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.854] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.855] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.855] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.855] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.855] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.875] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.876] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.876] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.877] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.877] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.891] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.892] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.892] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.892] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.892] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.918] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.918] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.919] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.919] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.919] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.931] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.931] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.931] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.931] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.932] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.947] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.948] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.949] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.949] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.949] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.964] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.964] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.964] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.964] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.964] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.980] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.981] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.981] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.981] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.981] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:31.997] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:31.997] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:31.997] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:31.997] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:31.997] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.049] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.050] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.050] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.050] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.050] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.064] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.064] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.064] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.064] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.064] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.080] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.081] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.081] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.081] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.081] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.097] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.097] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.098] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.098] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.098] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.113] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.114] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.114] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.114] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.114] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.130] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.130] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.130] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.130] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.130] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.148] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.148] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.148] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.148] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.149] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.163] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.164] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.164] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.164] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.164] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.180] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.180] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.180] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.180] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.181] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.197] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.197] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.197] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.197] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.197] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.214] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.214] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.214] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.214] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.214] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.233] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.233] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.233] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.233] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.234] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.251] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.251] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.251] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.251] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.251] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.267] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.268] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.268] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.268] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.268] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.284] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.284] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.284] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.285] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.285] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.301] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.301] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.301] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.301] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.301] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.319] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.319] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.319] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.319] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.319] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.334] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.334] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.335] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.335] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.335] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.353] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.353] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.353] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.353] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.354] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.369] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.369] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.369] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.369] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.369] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.385] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.386] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.386] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.386] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.386] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.402] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.402] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.402] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.402] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.402] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.421] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.421] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.421] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.421] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.421] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.436] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.436] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.436] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.436] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.437] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.453] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.453] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.453] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.453] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.453] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.469] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.470] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.470] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.470] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.470] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.486] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.486] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.486] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.486] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.487] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.503] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.503] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.503] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.503] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.503] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.521] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.521] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.521] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.521] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.521] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.536] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.536] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.536] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.537] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.537] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.552] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.553] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.553] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.553] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.553] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.570] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.570] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.570] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.570] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.570] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.586] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.587] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.587] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.587] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.588] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.603] [TRACE] (main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:25:32.603] [ERROR] (main.cpp:429) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:25:32.603] [TRACE] (main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[2025-06-27 20:25:32.603] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:25:32.603] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:25:32.619] [ INFO] (main.cpp:439) Terminating GLFW and cleaning up resources...
[2025-06-27 20:25:32.632] [ INFO] (main.cpp:448) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:26:48.338
================================================================================

[2025-06-27 20:26:48.338] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:26:48.338] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:26:48.524] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:26:48.527] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:26:48.527] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:26:48.562] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:26:48.562] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:26:48.562] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:26:48.562] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:26:48.563] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:26:48.563] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:26:48.564] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:26:48.564] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:26:48.564] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:26:48.564] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:26:48.564] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:26:48.564] [TRACE] (mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[2025-06-27 20:26:48.564] [TRACE] (mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[2025-06-27 20:26:48.565] [ INFO] (mesh.cpp:76) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:26:48.565] [ INFO] (mesh.cpp:11) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:26:48.565] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:26:48.565] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:26:48.565] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:26:48.565] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:26:48.565] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:26:48.565] [TRACE] (main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:26:48.565] [TRACE] (main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:26:48.565] [ INFO] (main.cpp:342) Starting main rendering loop...
[2025-06-27 20:26:48.565] [TRACE] (main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:26:48.565] [TRACE] (main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:26:48.565] [TRACE] (main.cpp:428) Model Matrix:
  [   0.499,    0.000,   -0.033,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.033,    0.000,    0.499,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:26:48.565] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.565] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.565] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.565] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.566] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.567] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.567] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.567] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.567] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.567] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.595] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.596] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.596] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.596] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.596] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.600] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.600] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.601] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.602] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.602] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.627] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.627] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.627] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.627] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.627] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.639] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.640] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.640] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.640] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.640] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.655] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.656] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.656] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.656] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.656] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.672] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.672] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.672] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.672] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.672] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.688] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.689] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.689] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.689] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.689] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.705] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.705] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.705] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.705] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.706] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.723] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.723] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.723] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.723] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.723] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.742] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.742] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.743] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.743] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.743] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.755] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.756] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.756] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.756] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.756] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.773] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.773] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.775] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.776] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.777] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.789] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.789] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.789] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.789] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.789] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.805] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.805] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.805] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.805] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.805] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.821] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.822] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.822] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.822] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.822] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.838] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.838] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.842] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.843] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.844] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.856] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.856] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.856] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.856] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.856] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.875] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.875] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.875] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.876] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.876] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.889] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.889] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.889] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.889] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.889] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.910] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.910] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.910] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.911] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.911] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.928] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.928] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.928] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.928] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.928] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.951] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.951] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.951] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.952] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.952] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.964] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.965] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.965] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.965] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.965] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.979] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.979] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.979] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.979] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.979] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:48.995] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:48.995] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:48.996] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:48.996] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:48.996] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.013] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.013] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.013] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.013] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.013] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.029] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.030] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.030] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.030] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.030] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.046] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.046] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.046] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.046] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.047] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.063] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.063] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.063] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.063] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.063] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.079] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.079] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.079] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.079] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.079] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.096] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.096] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.097] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.097] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.098] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.112] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.112] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.113] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.113] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.114] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.128] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.129] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.129] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.130] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.130] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.152] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.152] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.152] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.152] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.152] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.164] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.164] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.164] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.164] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.165] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.179] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.179] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.179] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.179] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.179] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.195] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.195] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.195] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.195] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.195] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.212] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.212] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.212] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.212] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.212] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.228] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.228] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.228] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.228] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.228] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.245] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.246] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.246] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.246] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.246] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.262] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.262] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.262] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.263] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.263] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.285] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.285] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.285] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.285] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.285] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.293] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.293] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.294] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.294] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.294] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.310] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.310] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.310] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.310] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.310] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.327] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.328] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.328] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.329] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.329] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.343] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.343] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.343] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.343] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.344] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.362] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.362] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.362] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.362] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.362] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.377] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.379] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.379] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.379] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.379] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.394] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.394] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.394] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.394] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.394] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.411] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.416] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.416] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.417] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.417] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.432] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.433] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.433] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.433] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.433] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.443] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.444] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.444] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.444] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.444] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.460] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.460] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.460] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.461] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.461] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.477] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.477] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.477] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.477] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.477] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.496] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.496] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.496] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.497] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.497] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.511] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.511] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.511] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.511] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.511] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.527] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.528] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.528] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.528] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.528] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.544] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.544] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.544] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.544] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.544] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.563] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.564] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.564] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.564] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.564] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.579] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.579] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.580] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.580] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.580] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.595] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.596] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.596] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.596] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.596] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.611] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.611] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.611] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.611] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.611] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.628] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.628] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.628] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.628] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.628] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.646] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.647] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.648] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.648] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.649] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.666] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.667] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.667] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.667] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.667] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.678] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.679] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.679] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.679] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.679] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.695] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.695] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.695] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.695] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.695] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.711] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.711] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.711] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.711] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.711] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.728] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.728] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.728] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.728] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.728] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.745] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.746] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.746] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.746] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.746] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.762] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.762] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.762] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.762] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.763] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.778] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:49.778] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:49.778] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[2025-06-27 20:26:49.778] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:49.778] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:49.795] [ INFO] (main.cpp:443) Terminating GLFW and cleaning up resources...
[2025-06-27 20:26:49.807] [ INFO] (main.cpp:452) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:26:56.422
================================================================================

[2025-06-27 20:26:56.422] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:26:56.422] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:26:56.726] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:26:56.730] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:26:56.730] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:26:56.730] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:26:56.730] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:26:56.730] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:26:56.731] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:26:56.731] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:26:56.750] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:26:56.750] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:26:56.751] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:26:56.751] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:26:56.752] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:26:56.753] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:26:56.753] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:26:56.753] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:26:56.753] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:26:56.753] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:26:56.753] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:26:56.755] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:26:56.755] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:26:56.755] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:26:56.755] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:26:56.755] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:26:56.755] [TRACE] (mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[2025-06-27 20:26:56.755] [TRACE] (mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[2025-06-27 20:26:56.757] [ INFO] (mesh.cpp:76) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:26:56.757] [ INFO] (mesh.cpp:11) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:26:56.757] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:26:56.757] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:26:56.757] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:26:56.757] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:26:56.757] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:26:56.757] [TRACE] (main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:26:56.757] [TRACE] (main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:26:56.758] [ INFO] (main.cpp:342) Starting main rendering loop...
[2025-06-27 20:26:56.758] [TRACE] (main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:26:56.758] [TRACE] (main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:26:56.758] [TRACE] (main.cpp:428) Model Matrix:
  [   0.498,    0.000,   -0.049,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.049,    0.000,    0.498,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:26:56.758] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.758] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.758] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.759] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.759] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.760] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.761] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.761] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.761] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.761] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.794] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.796] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.796] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.796] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.796] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.815] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.816] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.816] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.816] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.816] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.828] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.829] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.829] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.830] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.830] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.845] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.846] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.846] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.847] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.847] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.862] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.863] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.863] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.864] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.865] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.879] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.879] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.880] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.880] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.881] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.895] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.895] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.896] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.896] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.896] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.914] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.914] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.915] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.915] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.915] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.930] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.930] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.930] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.930] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.931] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.945] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.946] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.946] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.947] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.947] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.961] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.962] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.962] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.962] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.962] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.978] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.978] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.978] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.978] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.978] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:56.995] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:56.995] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:56.995] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:56.995] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:56.995] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.013] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.013] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.013] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.013] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.013] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.029] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.030] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.031] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.031] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.031] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.045] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.045] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.045] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.045] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.046] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.070] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.070] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.071] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.071] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.072] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.085] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.085] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.085] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.085] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.086] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.108] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.108] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.108] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.109] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.109] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.124] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.125] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.125] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.125] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.125] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.142] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.142] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.142] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.142] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.143] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.158] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.158] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.158] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.158] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.159] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.176] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.176] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.176] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.176] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.176] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.194] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.194] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.195] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.195] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.195] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.209] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.210] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.210] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.210] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.210] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.225] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.226] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.226] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.226] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.226] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.242] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.242] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.243] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.243] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.243] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.259] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.259] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.259] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.259] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.259] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.276] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.276] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.276] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.276] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.277] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.292] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.293] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.293] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.293] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.293] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.310] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.310] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.310] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.310] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.310] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.326] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.326] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.326] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.327] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.327] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.342] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.343] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.343] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.343] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.343] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.360] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.360] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.361] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.361] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.362] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.376] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.377] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.377] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.377] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.377] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.393] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.394] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.394] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.394] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.394] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.409] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.410] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.410] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.410] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.410] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.427] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.427] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.427] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.427] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.428] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.443] [TRACE] (main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:26:57.444] [ERROR] (main.cpp:433) [Rendering] No VAO bound during Before mesh draw
[2025-06-27 20:26:57.444] [TRACE] (main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[2025-06-27 20:26:57.444] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:26:57.444] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:26:57.460] [ INFO] (main.cpp:443) Terminating GLFW and cleaning up resources...
[2025-06-27 20:26:57.485] [ INFO] (main.cpp:452) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:27:04.177
================================================================================

[2025-06-27 20:27:04.177] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:27:04.177] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:27:04.442] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:27:04.445] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:27:04.446] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:27:04.462] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:27:04.462] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:27:04.462] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:27:04.462] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:27:04.463] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:27:04.464] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:27:04.464] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:27:04.464] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:27:04.464] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:27:04.464] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:27:04.464] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:27:04.464] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:27:04.464] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:27:04.465] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:27:04.465] [ INFO] (main.cpp:307) Creating mesh with 3 vertices and 3 indices
[2025-06-27 20:27:04.465] [TRACE] (mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[2025-06-27 20:27:04.465] [TRACE] (mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[2025-06-27 20:27:04.467] [ INFO] (mesh.cpp:76) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:27:04.467] [ INFO] (mesh.cpp:11) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:27:04.467] [ INFO] (main.cpp:309) Mesh created successfully
[2025-06-27 20:27:04.467] [ INFO] (main.cpp:313) Initializing animation manager...
[2025-06-27 20:27:04.467] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:27:04.467] [ INFO] (main.cpp:321) No animations found in glTF model
[2025-06-27 20:27:04.467] [ INFO] (main.cpp:325) Setting up OpenGL state...
[2025-06-27 20:27:04.467] [TRACE] (main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:27:04.467] [TRACE] (main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:27:04.467] [ INFO] (main.cpp:342) Starting main rendering loop...
[2025-06-27 20:27:04.467] [TRACE] (main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:27:04.468] [TRACE] (main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:27:04.468] [TRACE] (main.cpp:428) Model Matrix:
  [   0.498,    0.000,   -0.042,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.042,    0.000,    0.498,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[2025-06-27 20:27:04.468] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.468] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.468] [TRACE] (main.cpp:438) OpenGL State Validation [After mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:27:04.468] [ERROR] (main.cpp:438) [Rendering] No VAO bound during After mesh draw
[2025-06-27 20:27:04.469] [TRACE] (main.cpp:438) Stack trace:
  ./Simple3DEngine(+0x62f43) [0x5588279c0f43]
  ./Simple3DEngine(+0x6358a) [0x5588279c158a]
  ./Simple3DEngine(+0x172dd) [0x5588279752dd]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fa3bfe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fa3bfe46305]
  ./Simple3DEngine(+0x10f01) [0x55882796ef01]

[2025-06-27 20:27:04.471] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.471] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.512] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.514] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.539] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.540] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.551] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.552] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.565] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.567] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.583] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.584] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.598] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.599] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.615] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.615] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.632] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.633] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.649] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.649] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.665] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.665] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.682] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.683] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.705] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.706] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.717] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.717] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.733] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.733] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.749] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.750] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.765] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.766] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.785] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.785] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.802] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.802] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.823] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.823] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.838] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.838] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.856] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.857] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.872] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.872] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.904] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.904] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.922] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.922] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.938] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.939] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.956] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.957] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.972] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.972] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:04.988] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:04.988] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.005] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.006] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.022] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.022] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.042] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.042] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.055] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.056] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.071] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.071] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.092] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.092] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.107] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.108] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.139] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.139] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.153] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.153] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.169] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.169] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.186] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.188] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.204] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.204] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.219] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.219] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.236] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.236] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.252] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.253] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.269] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.269] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.286] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.286] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.303] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.303] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.320] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.320] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.336] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.336] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.353] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.353] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.369] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.370] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.386] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.386] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.403] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.403] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.419] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.419] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.439] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.439] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.456] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.456] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.471] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.471] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.491] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.492] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.507] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.507] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.523] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.524] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.540] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.540] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.557] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.557] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.574] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.574] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.591] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.591] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.607] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.608] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.623] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.624] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.640] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.640] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.657] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.657] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.673] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.673] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.690] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.690] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.707] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.707] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.724] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.724] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.740] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.740] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.757] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.757] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.773] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.774] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.790] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.790] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.807] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.807] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.824] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.824] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.839] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.840] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.857] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.857] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.874] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.874] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.890] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.890] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.907] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.907] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.923] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.923] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.940] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.940] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.956] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.957] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.973] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.974] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:05.991] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:05.991] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.011] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.011] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.023] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.024] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.055] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.055] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.072] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.072] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.087] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.087] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.104] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.104] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.121] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.121] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.137] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.138] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.154] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.155] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.170] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.170] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.188] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.188] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.204] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.204] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.221] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.221] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.237] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.237] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.254] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.255] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.270] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.271] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.288] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.288] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.305] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.306] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.321] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:06.322] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:06.338] [ INFO] (main.cpp:446) Terminating GLFW and cleaning up resources...
[2025-06-27 20:27:06.351] [ INFO] (main.cpp:455) === Simple 3D Engine shutdown complete ===

================================================================================
Simple 3D Engine Log - Session Started: 2025-06-27 20:27:54.805
================================================================================

[2025-06-27 20:27:54.806] [ INFO] (main.cpp:83) === Starting Simple 3D Engine with Enhanced Logging ===
[2025-06-27 20:27:54.806] [ INFO] (main.cpp:84) Logging system initialized with colors and detailed tracing
[2025-06-27 20:27:55.220] [ INFO] (main.cpp:104) GLFW window created successfully (800x600)
[2025-06-27 20:27:55.229] [ INFO] (main.cpp:113) OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-06-27 20:27:55.230] [ INFO] (main.cpp:114) GLSL Version: 4.60
[2025-06-27 20:27:55.231] [ INFO] (main.cpp:115) OpenGL Vendor: Intel
[2025-06-27 20:27:55.231] [ INFO] (main.cpp:116) OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-06-27 20:27:55.231] [ INFO] (main.cpp:120) Max vertex attributes: 16
[2025-06-27 20:27:55.231] [ INFO] (main.cpp:131) Loading shaders...
[2025-06-27 20:27:55.232] [ INFO] (shader.cpp:30) Shader files loaded: ../src/simple.vert, ../src/simple.frag
[2025-06-27 20:27:55.248] [ INFO] (shader.cpp:77) Shader program created successfully with ID: 3
[2025-06-27 20:27:55.249] [ INFO] (main.cpp:133) Shader program compiled successfully with ID: 3
[2025-06-27 20:27:55.249] [ INFO] (main.cpp:137) Loading glTF file: ../test.gltf
[2025-06-27 20:27:55.249] [ INFO] Successfully read file: ../test.gltf (1486 bytes)
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:147) glTF file parsed successfully
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:148)   Nodes: 1
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:149)   Meshes: 1
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:150)   Materials: 0
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:151)   Animations: 0
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:152)   Skins: 0
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:153)   Buffers: 1
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:171) Using mesh 0, primitive 0
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:172) Primitive attributes:
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:174)   NORMAL: accessor 1
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:174)   POSITION: accessor 0
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:176) Primitive indices: 2
[2025-06-27 20:27:55.250] [ INFO] (main.cpp:188) Loading 3 vertices
[2025-06-27 20:27:55.250] [TRACE] (main.cpp:209) First 3 vertex positions:
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[2025-06-27 20:27:55.251] [ INFO] (main.cpp:214) Position data loaded successfully
[2025-06-27 20:27:55.251] [ INFO] (main.cpp:307) Creating test triangle for visibility verification...
[2025-06-27 20:27:55.251] [ INFO] (main.cpp:315) Test triangle vertices:
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:317) Vertex 0: (-0.500, -0.500, 0.000)
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:317) Vertex 1: (0.500, -0.500, 0.000)
[2025-06-27 20:27:55.251] [TRACE] (main.cpp:317) Vertex 2: (0.000, 0.500, 0.000)
[2025-06-27 20:27:55.251] [ INFO] (main.cpp:321) Creating mesh with 3 test vertices and 3 indices
[2025-06-27 20:27:55.251] [TRACE] (mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[2025-06-27 20:27:55.251] [TRACE] (mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[2025-06-27 20:27:55.253] [ INFO] (mesh.cpp:76) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 20:27:55.253] [ INFO] (mesh.cpp:11) Mesh created with 3 vertices and 3 indices.
[2025-06-27 20:27:55.253] [ INFO] (main.cpp:323) Test mesh created successfully
[2025-06-27 20:27:55.253] [ INFO] (main.cpp:327) Initializing animation manager...
[2025-06-27 20:27:55.253] [ INFO] AnimationManager initialized with 0 animations
[2025-06-27 20:27:55.253] [ INFO] (main.cpp:335) No animations found in glTF model
[2025-06-27 20:27:55.253] [ INFO] (main.cpp:339) Setting up OpenGL state...
[2025-06-27 20:27:55.253] [TRACE] (main.cpp:352) Initial camera position: (0.000, 0.000, 3.000)
[2025-06-27 20:27:55.253] [TRACE] (main.cpp:353) Initial camera front: (-0.000, 0.000, -1.000)
[2025-06-27 20:27:55.253] [ INFO] (main.cpp:356) Starting main rendering loop...
[2025-06-27 20:27:55.254] [TRACE] (main.cpp:404) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[2025-06-27 20:27:55.254] [TRACE] (main.cpp:405) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[2025-06-27 20:27:55.255] [TRACE] (main.cpp:441) Model Matrix:
  [   0.987,    0.161,    0.000,    0.000]
  [  -0.161,    0.987,    0.000,    0.000]
  [   0.000,    0.000,    1.000,    0.000]
  [   0.000,    0.000,   -1.000,    1.000]

[2025-06-27 20:27:55.255] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.255] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.255] [TRACE] (main.cpp:451) OpenGL State Validation [After mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[2025-06-27 20:27:55.256] [ERROR] (main.cpp:451) [Rendering] No VAO bound during After mesh draw
[2025-06-27 20:27:55.256] [TRACE] (main.cpp:451) Stack trace:
  ./Simple3DEngine(+0x63e11) [0x561ee4a9fe11]
  ./Simple3DEngine(+0x64458) [0x561ee4aa0458]
  ./Simple3DEngine(+0x17881) [0x561ee4a53881]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f9af864624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f9af8646305]
  ./Simple3DEngine(+0x10f01) [0x561ee4a4cf01]

[2025-06-27 20:27:55.258] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.258] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.298] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.300] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.312] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.313] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.326] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.326] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.342] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.343] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.360] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.360] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.375] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.375] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.391] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.392] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.409] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.409] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.425] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.425] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.442] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.442] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.459] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.459] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.475] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.475] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.492] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.493] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.509] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.509] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.528] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.529] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.546] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.546] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.571] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.571] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.587] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.587] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.606] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.607] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.623] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.623] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.639] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.639] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.656] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.656] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.674] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.675] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.690] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.690] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.707] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.707] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.724] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.724] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.740] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.740] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.757] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.757] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.773] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.773] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.790] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.790] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.807] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.807] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.825] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.825] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.841] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.841] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.857] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.857] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.874] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.874] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.890] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.891] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.907] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.907] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.924] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.924] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.941] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.941] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.957] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.958] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.975] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.976] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:55.991] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:55.992] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.008] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.008] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.025] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.025] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.040] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.041] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.071] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.072] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.087] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.087] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.105] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.105] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.121] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.121] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.138] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.138] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.154] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.155] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.171] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.171] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.188] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.188] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.205] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.205] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.221] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.221] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.238] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.238] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.254] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.255] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.271] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.271] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.288] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.288] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.305] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.305] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.321] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.321] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.340] [TRACE] (mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[2025-06-27 20:27:56.340] [TRACE] (mesh.cpp:31) Mesh drawn successfully
[2025-06-27 20:27:56.357] [ INFO] (main.cpp:459) Terminating GLFW and cleaning up resources...
[2025-06-27 20:27:56.369] [ INFO] (main.cpp:468) === Simple 3D Engine shutdown complete ===
