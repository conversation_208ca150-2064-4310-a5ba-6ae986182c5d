# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/test1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/test1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test1/build/CMakeFiles /home/<USER>/test_google_cli/test1/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test_google_cli/test1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named glad

# Build rule for target.
glad: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 glad
.PHONY : glad

# fast build rule for target.
glad/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/glad.dir/build.make CMakeFiles/glad.dir/build
.PHONY : glad/fast

#=============================================================================
# Target rules for targets named Simple3DEngine

# Build rule for target.
Simple3DEngine: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Simple3DEngine
.PHONY : Simple3DEngine

# fast build rule for target.
Simple3DEngine/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/build
.PHONY : Simple3DEngine/fast

external/src/glad.o: external/src/glad.c.o
.PHONY : external/src/glad.o

# target to build an object file
external/src/glad.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/glad.dir/build.make CMakeFiles/glad.dir/external/src/glad.c.o
.PHONY : external/src/glad.c.o

external/src/glad.i: external/src/glad.c.i
.PHONY : external/src/glad.i

# target to preprocess a source file
external/src/glad.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/glad.dir/build.make CMakeFiles/glad.dir/external/src/glad.c.i
.PHONY : external/src/glad.c.i

external/src/glad.s: external/src/glad.c.s
.PHONY : external/src/glad.s

# target to generate assembly for a file
external/src/glad.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/glad.dir/build.make CMakeFiles/glad.dir/external/src/glad.c.s
.PHONY : external/src/glad.c.s

gltf_parser/src/gltf.o: gltf_parser/src/gltf.cpp.o
.PHONY : gltf_parser/src/gltf.o

# target to build an object file
gltf_parser/src/gltf.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.o
.PHONY : gltf_parser/src/gltf.cpp.o

gltf_parser/src/gltf.i: gltf_parser/src/gltf.cpp.i
.PHONY : gltf_parser/src/gltf.i

# target to preprocess a source file
gltf_parser/src/gltf.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.i
.PHONY : gltf_parser/src/gltf.cpp.i

gltf_parser/src/gltf.s: gltf_parser/src/gltf.cpp.s
.PHONY : gltf_parser/src/gltf.s

# target to generate assembly for a file
gltf_parser/src/gltf.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/gltf_parser/src/gltf.cpp.s
.PHONY : gltf_parser/src/gltf.cpp.s

json_parser/src/json.o: json_parser/src/json.cpp.o
.PHONY : json_parser/src/json.o

# target to build an object file
json_parser/src/json.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.o
.PHONY : json_parser/src/json.cpp.o

json_parser/src/json.i: json_parser/src/json.cpp.i
.PHONY : json_parser/src/json.i

# target to preprocess a source file
json_parser/src/json.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.i
.PHONY : json_parser/src/json.cpp.i

json_parser/src/json.s: json_parser/src/json.cpp.s
.PHONY : json_parser/src/json.s

# target to generate assembly for a file
json_parser/src/json.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/json_parser/src/json.cpp.s
.PHONY : json_parser/src/json.cpp.s

src/camera.o: src/camera.cpp.o
.PHONY : src/camera.o

# target to build an object file
src/camera.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/camera.cpp.o
.PHONY : src/camera.cpp.o

src/camera.i: src/camera.cpp.i
.PHONY : src/camera.i

# target to preprocess a source file
src/camera.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/camera.cpp.i
.PHONY : src/camera.cpp.i

src/camera.s: src/camera.cpp.s
.PHONY : src/camera.s

# target to generate assembly for a file
src/camera.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/camera.cpp.s
.PHONY : src/camera.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/mesh.o: src/mesh.cpp.o
.PHONY : src/mesh.o

# target to build an object file
src/mesh.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.o
.PHONY : src/mesh.cpp.o

src/mesh.i: src/mesh.cpp.i
.PHONY : src/mesh.i

# target to preprocess a source file
src/mesh.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.i
.PHONY : src/mesh.cpp.i

src/mesh.s: src/mesh.cpp.s
.PHONY : src/mesh.s

# target to generate assembly for a file
src/mesh.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/mesh.cpp.s
.PHONY : src/mesh.cpp.s

src/shader.o: src/shader.cpp.o
.PHONY : src/shader.o

# target to build an object file
src/shader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/shader.cpp.o
.PHONY : src/shader.cpp.o

src/shader.i: src/shader.cpp.i
.PHONY : src/shader.i

# target to preprocess a source file
src/shader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/shader.cpp.i
.PHONY : src/shader.cpp.i

src/shader.s: src/shader.cpp.s
.PHONY : src/shader.s

# target to generate assembly for a file
src/shader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/src/shader.cpp.s
.PHONY : src/shader.cpp.s

utils/src/base_encoding.o: utils/src/base_encoding.cpp.o
.PHONY : utils/src/base_encoding.o

# target to build an object file
utils/src/base_encoding.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.o
.PHONY : utils/src/base_encoding.cpp.o

utils/src/base_encoding.i: utils/src/base_encoding.cpp.i
.PHONY : utils/src/base_encoding.i

# target to preprocess a source file
utils/src/base_encoding.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.i
.PHONY : utils/src/base_encoding.cpp.i

utils/src/base_encoding.s: utils/src/base_encoding.cpp.s
.PHONY : utils/src/base_encoding.s

# target to generate assembly for a file
utils/src/base_encoding.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/base_encoding.cpp.s
.PHONY : utils/src/base_encoding.cpp.s

utils/src/directory_utils.o: utils/src/directory_utils.cpp.o
.PHONY : utils/src/directory_utils.o

# target to build an object file
utils/src/directory_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.o
.PHONY : utils/src/directory_utils.cpp.o

utils/src/directory_utils.i: utils/src/directory_utils.cpp.i
.PHONY : utils/src/directory_utils.i

# target to preprocess a source file
utils/src/directory_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.i
.PHONY : utils/src/directory_utils.cpp.i

utils/src/directory_utils.s: utils/src/directory_utils.cpp.s
.PHONY : utils/src/directory_utils.s

# target to generate assembly for a file
utils/src/directory_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/directory_utils.cpp.s
.PHONY : utils/src/directory_utils.cpp.s

utils/src/error_handler.o: utils/src/error_handler.cpp.o
.PHONY : utils/src/error_handler.o

# target to build an object file
utils/src/error_handler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.o
.PHONY : utils/src/error_handler.cpp.o

utils/src/error_handler.i: utils/src/error_handler.cpp.i
.PHONY : utils/src/error_handler.i

# target to preprocess a source file
utils/src/error_handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.i
.PHONY : utils/src/error_handler.cpp.i

utils/src/error_handler.s: utils/src/error_handler.cpp.s
.PHONY : utils/src/error_handler.s

# target to generate assembly for a file
utils/src/error_handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/error_handler.cpp.s
.PHONY : utils/src/error_handler.cpp.s

utils/src/logger.o: utils/src/logger.cpp.o
.PHONY : utils/src/logger.o

# target to build an object file
utils/src/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.o
.PHONY : utils/src/logger.cpp.o

utils/src/logger.i: utils/src/logger.cpp.i
.PHONY : utils/src/logger.i

# target to preprocess a source file
utils/src/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.i
.PHONY : utils/src/logger.cpp.i

utils/src/logger.s: utils/src/logger.cpp.s
.PHONY : utils/src/logger.s

# target to generate assembly for a file
utils/src/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Simple3DEngine.dir/build.make CMakeFiles/Simple3DEngine.dir/utils/src/logger.cpp.s
.PHONY : utils/src/logger.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... Simple3DEngine"
	@echo "... glad"
	@echo "... external/src/glad.o"
	@echo "... external/src/glad.i"
	@echo "... external/src/glad.s"
	@echo "... gltf_parser/src/gltf.o"
	@echo "... gltf_parser/src/gltf.i"
	@echo "... gltf_parser/src/gltf.s"
	@echo "... json_parser/src/json.o"
	@echo "... json_parser/src/json.i"
	@echo "... json_parser/src/json.s"
	@echo "... src/camera.o"
	@echo "... src/camera.i"
	@echo "... src/camera.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/mesh.o"
	@echo "... src/mesh.i"
	@echo "... src/mesh.s"
	@echo "... src/shader.o"
	@echo "... src/shader.i"
	@echo "... src/shader.s"
	@echo "... utils/src/base_encoding.o"
	@echo "... utils/src/base_encoding.i"
	@echo "... utils/src/base_encoding.s"
	@echo "... utils/src/directory_utils.o"
	@echo "... utils/src/directory_utils.i"
	@echo "... utils/src/directory_utils.s"
	@echo "... utils/src/error_handler.o"
	@echo "... utils/src/error_handler.i"
	@echo "... utils/src/error_handler.s"
	@echo "... utils/src/logger.o"
	@echo "... utils/src/logger.i"
	@echo "... utils/src/logger.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

