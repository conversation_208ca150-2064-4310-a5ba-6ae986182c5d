
--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:23:40.069
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:23:40.358 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:23:40.358 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:23:40.358 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:23:40.358 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:424) Model Matrix:
  [   1.994,    0.000,   -0.157,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.157,    0.000,    1.994,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.362 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.362 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.388 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.388 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.403 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.406 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.411 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.417 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.433 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.434 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.445 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.446 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.460 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.460 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.476 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.476 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.494 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.494 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.510 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.510 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.528 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.529 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.543 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.544 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.559 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.559 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.577 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.577 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.593 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.593 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.609 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.609 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.627 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.627 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.643 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.643 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.672 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.672 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.686 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.686 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.704 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.704 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.722 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.722 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.737 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.737 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.756 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.756 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.771 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.771 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.787 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.787 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.803 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.803 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.823 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.823 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.840 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.840 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.856 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.857 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.872 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.872 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.888 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.889 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.905 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.906 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.923 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.925 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.939 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.940 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.956 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.956 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.971 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.972 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:40.988 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:40.988 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.005 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.005 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.022 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.022 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.037 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.038 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.054 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.055 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.071 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.071 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.088 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.088 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.104 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.105 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.121 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.121 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.137 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.138 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.154 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.154 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.170 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.171 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.187 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.188 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.204 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.204 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.220 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.220 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.237 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.237 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.254 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.254 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.270 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.271 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.287 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.288 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.304 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.304 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.320 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.321 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.337 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.337 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.355 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.356 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.371 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.372 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.388 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.388 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.405 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.405 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.422 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.422 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.438 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.438 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.455 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.455 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.471 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.471 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.488 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.488 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.505 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.505 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.521 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.521 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.538 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.538 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.555 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.555 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.571 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.571 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.588 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.588 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.605 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.605 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.621 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.622 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.638 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.638 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.654 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.655 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.671 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.671 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.688 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.688 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.704 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.705 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.722 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.723 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.738 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.739 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.771 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.771 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.788 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.788 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.804 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.805 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.821 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.821 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.838 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.838 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.854 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.855 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.871 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.871 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.888 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.888 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.904 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.905 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.921 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.921 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.938 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.938 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.954 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.955 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.971 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.971 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:41.988 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:41.988 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.005 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.005 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.021 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.022 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.037 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.037 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.054 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.054 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.070 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.070 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.087 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.087 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.104 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.104 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.121 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.121 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.137 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.137 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.154 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.155 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.170 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.171 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.187 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.187 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.203 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.204 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.220 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.220 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]

[TRACE] 2025-06-27 20:23:42.237 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:42.237 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x5653ea205ded]
  ./Simple3DEngine(+0x62434) [0x5653ea206434]
  ./Simple3DEngine(+0x171ec) [0x5653ea1bb1ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f0290e4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f0290e46305]
  ./Simple3DEngine(+0x10f01) [0x5653ea1b4f01]


--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:23:50.016
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:23:50.217 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:23:50.217 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:23:50.217 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:23:50.217 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:23:50.221 (/home/<USER>/test_google_cli/test1/src/main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:23:50.221 (/home/<USER>/test_google_cli/test1/src/main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:23:50.221 (/home/<USER>/test_google_cli/test1/src/main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:23:50.221 (/home/<USER>/test_google_cli/test1/src/main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:23:50.222 (/home/<USER>/test_google_cli/test1/src/main.cpp:424) Model Matrix:
  [   1.998,    0.000,   -0.098,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.098,    0.000,    1.998,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:23:50.222 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.222 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.225 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.225 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.260 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.260 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.267 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.267 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.296 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.297 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.313 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.313 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.330 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.330 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.346 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.346 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.364 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.365 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.380 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.381 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.398 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.398 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.415 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.415 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.431 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.431 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.447 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.448 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.481 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.481 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.498 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.498 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.515 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.515 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.531 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.532 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.553 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.554 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.568 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.568 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.583 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.583 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.600 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.600 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.616 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.617 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.635 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.635 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.652 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.652 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.667 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.668 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.683 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.683 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.701 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.701 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.720 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.721 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.737 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.737 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.752 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.752 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.769 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.769 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.786 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.786 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.802 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.802 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.819 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.819 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.836 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.836 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.852 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.853 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.868 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.868 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.885 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.885 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.902 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.902 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.918 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.919 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.935 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.935 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.952 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.952 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.968 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.969 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:50.986 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:50.986 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.002 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.002 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.030 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.030 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.040 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.041 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.067 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.067 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.079 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.080 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.096 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.096 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.112 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.112 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.129 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.129 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.145 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.146 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.162 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.163 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.179 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.179 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.195 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.196 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.212 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.212 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.229 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.229 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.245 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.245 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.262 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.262 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.279 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.279 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.296 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.296 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.312 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.313 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.329 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.329 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.349 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.349 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.366 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.366 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.383 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.383 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.403 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.403 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.419 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.419 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.435 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.435 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.451 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.452 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.468 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.469 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.485 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.485 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.502 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.502 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.518 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.518 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.535 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.535 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.551 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.552 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.568 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.569 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.585 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.585 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.601 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.602 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.618 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.618 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.635 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.635 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.651 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.652 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.667 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.668 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.684 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.684 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.702 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.702 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.722 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.722 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.735 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.735 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.751 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.752 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.768 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.768 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.785 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.785 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.801 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.802 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.818 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.818 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.835 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.835 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.851 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.851 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.868 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.868 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.885 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.885 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.901 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.902 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.918 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.918 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.935 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.935 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.951 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.951 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]

[TRACE] 2025-06-27 20:23:51.968 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:23:51.968 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x61ded) [0x55e6aa5abded]
  ./Simple3DEngine(+0x62434) [0x55e6aa5ac434]
  ./Simple3DEngine(+0x171ec) [0x55e6aa5611ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fbee936624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fbee9366305]
  ./Simple3DEngine(+0x10f01) [0x55e6aa55af01]


--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:25:30.822
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:25:31.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:25:31.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:25:31.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:25:31.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:25:31.047 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[TRACE] 2025-06-27 20:25:31.047 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:334) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:335) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:386) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:387) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:424) Model Matrix:
  [   1.997,    0.000,   -0.116,    0.000]
  [   0.000,    2.000,    0.000,    0.000]
  [   0.116,    0.000,    1.997,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:25:31.048 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.049 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.049 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.049 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.050 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.050 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.050 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.051 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.070 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.071 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.071 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.071 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.072 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.072 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.073 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.101 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.101 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.101 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.101 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.116 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.117 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.117 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.117 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.133 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.133 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.133 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.133 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.149 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.149 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.150 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.150 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.167 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.167 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.167 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.167 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.184 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.184 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.184 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.184 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.203 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.203 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.218 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.218 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.218 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.218 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.235 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.235 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.236 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.236 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.252 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.254 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.271 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.271 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.271 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.272 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.288 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.289 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.289 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.289 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.299 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.301 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.302 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.302 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.318 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.318 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.318 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.318 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.333 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.333 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.333 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.334 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.352 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.352 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.352 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.352 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.371 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.371 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.372 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.372 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.387 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.387 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.387 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.387 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.402 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.403 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.404 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.404 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.416 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.416 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.417 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.417 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.432 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.433 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.434 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.434 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.448 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.448 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.448 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.448 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.463 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.463 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.463 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.464 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.479 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.479 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.479 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.480 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.496 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.496 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.496 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.496 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.513 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.513 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.513 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.513 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.529 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.529 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.529 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.530 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.546 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.546 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.546 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.546 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.562 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.563 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.563 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.563 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.580 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.580 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.581 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.581 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.596 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.597 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.597 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.597 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.613 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.613 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.613 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.613 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.629 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.629 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.630 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.630 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.649 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.649 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.666 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.666 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.666 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.666 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.684 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.684 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.684 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.685 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.701 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.701 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.701 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.701 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.717 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.717 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.717 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.717 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.738 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.738 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.738 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.738 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.754 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.758 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.758 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.770 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.770 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.770 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.770 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.787 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.787 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.787 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.787 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.803 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.803 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.803 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.804 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.820 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.821 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.821 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.821 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.837 (/home/<USER>/test_google_cli/test1/src/main.cpp:482) Mouse input initialized at (1.070312, 2.675781)
[TRACE] 2025-06-27 20:25:31.837 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.837 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.837 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.837 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.855 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.855 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.855 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.855 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.876 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.877 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.877 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.877 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.891 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.892 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.892 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.892 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.918 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.919 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.919 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.919 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.931 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.931 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.932 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.932 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.948 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.949 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.949 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.949 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.964 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.964 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.964 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.964 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.980 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.981 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.981 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.981 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:31.997 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:31.997 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:31.997 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:31.997 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.050 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.050 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.050 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.050 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.064 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.064 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.064 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.064 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.081 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.081 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.081 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.081 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.097 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.098 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.098 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.098 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.114 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.114 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.114 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.114 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.130 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.130 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.130 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.130 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.148 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.148 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.148 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.149 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.163 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.164 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.164 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.164 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.180 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.180 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.180 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.181 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.197 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.197 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.197 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.197 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.214 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.214 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.214 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.214 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.233 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.233 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.234 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.234 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.251 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.251 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.268 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.268 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.268 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.268 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.284 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.285 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.285 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.285 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.301 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.301 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.301 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.301 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.319 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.319 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.319 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.320 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.334 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.335 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.335 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.335 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.353 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.353 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.354 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.354 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.369 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.369 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.369 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.369 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.385 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.386 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.386 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.386 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.402 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.402 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.402 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.403 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.421 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.421 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.421 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.421 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.436 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.436 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.436 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.437 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.453 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.453 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.453 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.453 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.469 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.470 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.470 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.470 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.486 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.486 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.487 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.487 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.503 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.503 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.503 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.503 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.521 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.521 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.521 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.521 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.536 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.536 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.537 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.537 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.553 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.553 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.553 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.553 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.570 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.570 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.570 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.570 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.587 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.587 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.588 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.588 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:25:32.603 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:25:32.603 (/home/<USER>/test_google_cli/test1/src/main.cpp:429) Stack trace:
  ./Simple3DEngine(+0x62f21) [0x5624d24fdf21]
  ./Simple3DEngine(+0x63568) [0x5624d24fe568]
  ./Simple3DEngine(+0x171ec) [0x5624d24b21ec]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f398336624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f3983366305]
  ./Simple3DEngine(+0x10f01) [0x5624d24abf01]

[TRACE] 2025-06-27 20:25:32.603 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:25:32.603 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully

--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:26:48.338
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:26:48.563 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:26:48.564 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:26:48.564 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:26:48.564 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:26:48.564 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[TRACE] 2025-06-27 20:26:48.564 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:428) Model Matrix:
  [   0.499,    0.000,   -0.033,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.033,    0.000,    0.499,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.565 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.566 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.567 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.567 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.567 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.567 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.596 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.596 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.596 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.596 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.600 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.601 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.602 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.602 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.627 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.627 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.627 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.627 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.639 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.640 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.640 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.640 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.655 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.656 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.656 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.656 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.672 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.672 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.672 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.672 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.688 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.689 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.689 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.689 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.705 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.705 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.706 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.706 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.723 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.723 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.723 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.723 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.742 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.743 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.743 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.743 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.756 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.756 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.756 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.773 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.776 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.777 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.777 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.789 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.789 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.789 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.789 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.805 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.805 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.805 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.805 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.822 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.822 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.822 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.822 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.838 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.843 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.844 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.844 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.856 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.856 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.856 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.856 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.875 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.876 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.876 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.876 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.889 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.889 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.889 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.889 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.910 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.911 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.911 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.911 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.928 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.928 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.928 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.928 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.951 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.951 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.952 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.952 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.964 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.965 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.965 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.965 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.979 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.979 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.979 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.979 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:48.995 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:48.996 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:48.996 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:48.996 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.013 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.013 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.013 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.014 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.029 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.030 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.030 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.030 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.046 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.046 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.047 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.063 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.063 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.063 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.063 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.079 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.079 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.079 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.079 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.096 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.097 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.098 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.099 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.112 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.113 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.114 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.115 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.128 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.130 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.130 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.131 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.152 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.152 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.152 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.152 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.164 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.164 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.164 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.165 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.179 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.179 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.179 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.180 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.195 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.195 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.195 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.195 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.212 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.212 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.212 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.212 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.228 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.228 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.228 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.228 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.246 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.246 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.246 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.246 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.262 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.263 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.263 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.263 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.285 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.285 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.285 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.285 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.293 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.294 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.294 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.294 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.310 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.310 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.310 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.310 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.328 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.329 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.329 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.330 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.343 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.343 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.344 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.344 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.362 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.362 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.362 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.362 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.377 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.379 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.379 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.379 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.394 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.394 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.394 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.395 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.412 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.417 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.417 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.417 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.433 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.433 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.433 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.433 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.443 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.444 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.444 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.444 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.460 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.460 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.461 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.461 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.477 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.477 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.477 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.477 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.496 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.496 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.497 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.497 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.511 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.511 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.511 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.511 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.528 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.528 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.528 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.528 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.544 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.544 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.544 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.544 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.563 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.564 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.564 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.564 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.579 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.580 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.580 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.580 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.595 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.596 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.596 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.596 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.611 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.611 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.611 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.612 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.628 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.628 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.628 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.628 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.646 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.648 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.666 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.667 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.667 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.667 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.678 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.679 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.679 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.679 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.695 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.695 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.695 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.695 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.711 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.711 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.711 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.712 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.728 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.728 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.728 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.728 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.745 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.746 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.746 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.746 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.762 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.762 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.763 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.763 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:49.778 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:49.778 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x556e541a7f05]
  ./Simple3DEngine(+0x6354c) [0x556e541a854c]
  ./Simple3DEngine(+0x171d0) [0x556e5415c1d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fee71c4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fee71c46305]
  ./Simple3DEngine(+0x10f01) [0x556e54155f01]

[TRACE] 2025-06-27 20:26:49.778 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:49.778 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully

--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:26:56.422
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:26:56.753 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:26:56.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:26:56.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:26:56.755 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:26:56.755 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[TRACE] 2025-06-27 20:26:56.755 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[TRACE] 2025-06-27 20:26:56.757 (/home/<USER>/test_google_cli/test1/src/main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:26:56.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:26:56.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:26:56.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:26:56.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:428) Model Matrix:
  [   0.498,    0.000,   -0.049,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.049,    0.000,    0.498,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:26:56.758 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.759 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.759 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.759 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.760 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.761 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.761 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.761 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.796 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.796 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.796 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.796 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.816 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.816 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.816 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.816 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.829 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.830 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.830 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.830 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.846 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.847 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.847 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.847 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.863 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.864 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.864 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.865 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.879 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.880 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.881 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.881 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.895 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.896 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.896 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.896 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.914 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.915 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.915 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.915 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.930 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.930 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.931 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.931 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.946 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.947 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.947 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.947 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.962 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.962 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.962 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.962 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.978 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.978 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.978 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.979 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:56.995 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:56.995 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:56.995 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:56.995 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.013 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.013 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.013 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.013 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.030 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.031 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.031 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.031 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.045 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.045 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.045 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.046 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.070 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.071 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.085 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.085 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.085 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.086 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.108 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.109 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.109 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.109 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.125 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.125 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.125 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.125 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.142 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.142 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.142 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.143 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.158 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.158 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.158 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.159 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.176 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.176 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.176 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.176 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.194 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.195 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.195 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.195 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.209 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.210 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.210 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.210 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.226 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.226 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.226 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.226 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.242 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.243 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.243 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.243 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.259 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.259 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.259 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.259 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.276 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.276 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.277 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.277 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.293 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.293 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.293 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.294 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.310 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.310 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.310 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.310 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.326 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.327 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.327 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.327 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.343 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.343 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.343 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.343 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.360 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.361 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.362 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.362 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.377 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.377 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.377 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.377 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.394 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.394 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.394 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.395 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.410 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.410 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.410 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.410 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.427 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.427 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.427 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.428 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:26:57.444 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) OpenGL State Validation [Before mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:26:57.444 (/home/<USER>/test_google_cli/test1/src/main.cpp:433) Stack trace:
  ./Simple3DEngine(+0x62f05) [0x5638adf74f05]
  ./Simple3DEngine(+0x6354c) [0x5638adf7554c]
  ./Simple3DEngine(+0x171d0) [0x5638adf291d0]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f8fabe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f8fabe46305]
  ./Simple3DEngine(+0x10f01) [0x5638adf22f01]

[TRACE] 2025-06-27 20:26:57.444 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:26:57.444 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully

--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:27:04.177
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:27:04.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:27:04.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:27:04.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:27:04.464 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:27:04.465 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[TRACE] 2025-06-27 20:27:04.465 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[TRACE] 2025-06-27 20:27:04.467 (/home/<USER>/test_google_cli/test1/src/main.cpp:338) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:27:04.467 (/home/<USER>/test_google_cli/test1/src/main.cpp:339) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:27:04.467 (/home/<USER>/test_google_cli/test1/src/main.cpp:390) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:27:04.468 (/home/<USER>/test_google_cli/test1/src/main.cpp:391) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:27:04.468 (/home/<USER>/test_google_cli/test1/src/main.cpp:428) Model Matrix:
  [   0.498,    0.000,   -0.042,    0.000]
  [   0.000,    0.500,    0.000,    0.000]
  [   0.042,    0.000,    0.498,    0.000]
  [   0.000,    0.000,   -2.000,    1.000]

[TRACE] 2025-06-27 20:27:04.468 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.468 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.468 (/home/<USER>/test_google_cli/test1/src/main.cpp:438) OpenGL State Validation [After mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:27:04.469 (/home/<USER>/test_google_cli/test1/src/main.cpp:438) Stack trace:
  ./Simple3DEngine(+0x62f43) [0x5588279c0f43]
  ./Simple3DEngine(+0x6358a) [0x5588279c158a]
  ./Simple3DEngine(+0x172dd) [0x5588279752dd]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7fa3bfe4624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7fa3bfe46305]
  ./Simple3DEngine(+0x10f01) [0x55882796ef01]

[TRACE] 2025-06-27 20:27:04.471 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.471 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.514 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.514 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.539 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.540 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.552 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.552 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.566 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.567 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.584 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.585 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.598 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.599 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.615 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.615 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.633 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.633 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.649 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.665 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.665 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.683 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.683 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.705 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.706 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.717 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.717 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.733 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.733 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.749 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.751 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.766 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.766 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.785 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.785 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.802 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.802 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.823 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.823 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.838 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.839 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.856 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.860 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.872 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.873 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.904 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.904 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.922 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.922 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.939 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.939 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.956 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.958 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.972 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.972 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:04.988 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:04.988 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.006 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.006 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.022 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.022 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.042 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.042 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.055 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.056 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.071 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.071 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.092 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.093 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.108 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.108 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.139 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.139 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.153 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.153 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.169 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.169 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.187 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.188 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.219 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.219 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.236 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.236 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.253 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.253 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.269 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.270 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.286 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.286 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.303 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.303 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.320 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.320 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.336 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.336 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.353 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.353 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.370 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.370 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.386 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.386 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.403 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.403 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.419 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.420 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.439 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.439 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.456 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.456 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.471 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.471 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.492 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.492 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.507 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.507 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.524 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.524 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.540 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.540 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.557 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.557 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.574 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.574 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.591 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.592 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.608 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.608 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.624 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.624 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.640 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.640 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.657 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.657 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.673 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.674 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.690 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.690 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.707 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.707 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.724 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.724 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.740 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.740 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.757 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.757 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.774 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.774 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.790 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.790 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.807 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.807 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.824 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.825 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.840 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.840 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.857 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.857 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.874 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.874 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.890 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.890 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.907 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.907 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.923 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.923 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.940 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.940 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.957 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.957 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.974 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.974 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:05.991 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:05.991 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.011 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.011 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.024 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.024 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.055 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.055 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.087 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.087 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.104 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.104 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.121 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.121 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.138 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.138 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.155 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.155 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.170 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.170 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.188 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.188 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.204 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.221 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.221 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.237 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.237 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.270 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.271 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.288 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.288 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.306 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.307 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:06.322 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:06.322 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully

--------------------------------------------------------------------------------
Trace Log - Session Started: 2025-06-27 20:27:54.806
--------------------------------------------------------------------------------

[TRACE] 2025-06-27 20:27:55.250 (/home/<USER>/test_google_cli/test1/src/main.cpp:209) First 3 vertex positions:
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 0: (0.500, 2.000, 0.000)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 1: (2.004, 0.000, -2.236)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:211) Vertex 2: (0.000, 0.000, 0.000)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:317) Vertex 0: (-0.500, -0.500, 0.000)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:317) Vertex 1: (0.500, -0.500, 0.000)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/main.cpp:317) Vertex 2: (0.000, 0.500, 0.000)
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:35) Setting up mesh with 3 vertices and 3 indices
[TRACE] 2025-06-27 20:27:55.251 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:41) Generated VAO: 1, VBO: 1, EBO: 2
[TRACE] 2025-06-27 20:27:55.253 (/home/<USER>/test_google_cli/test1/src/main.cpp:352) Initial camera position: (0.000, 0.000, 3.000)
[TRACE] 2025-06-27 20:27:55.253 (/home/<USER>/test_google_cli/test1/src/main.cpp:353) Initial camera front: (-0.000, 0.000, -1.000)
[TRACE] 2025-06-27 20:27:55.254 (/home/<USER>/test_google_cli/test1/src/main.cpp:404) Projection Matrix:
  [   1.811,    0.000,    0.000,    0.000]
  [   0.000,    2.414,    0.000,    0.000]
  [   0.000,    0.000,   -1.002,   -1.000]
  [   0.000,    0.000,   -0.200,    0.000]

[TRACE] 2025-06-27 20:27:55.254 (/home/<USER>/test_google_cli/test1/src/main.cpp:405) View Matrix:
  [   1.000,    0.000,    0.000,    0.000]
  [   0.000,    1.000,   -0.000,    0.000]
  [  -0.000,    0.000,    1.000,    0.000]
  [   0.000,   -0.000,   -3.000,    1.000]

[TRACE] 2025-06-27 20:27:55.255 (/home/<USER>/test_google_cli/test1/src/main.cpp:441) Model Matrix:
  [   0.987,    0.161,    0.000,    0.000]
  [  -0.161,    0.987,    0.000,    0.000]
  [   0.000,    0.000,    1.000,    0.000]
  [   0.000,    0.000,   -1.000,    1.000]

[TRACE] 2025-06-27 20:27:55.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.255 (/home/<USER>/test_google_cli/test1/src/main.cpp:451) OpenGL State Validation [After mesh draw]:
  Current Program: 3
  Current VAO: 0
  Depth Test: Enabled

[TRACE] 2025-06-27 20:27:55.256 (/home/<USER>/test_google_cli/test1/src/main.cpp:451) Stack trace:
  ./Simple3DEngine(+0x63e11) [0x561ee4a9fe11]
  ./Simple3DEngine(+0x64458) [0x561ee4aa0458]
  ./Simple3DEngine(+0x17881) [0x561ee4a53881]
  /lib/x86_64-linux-gnu/libc.so.6(+0x2724a) [0x7f9af864624a]
  /lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0x85) [0x7f9af8646305]
  ./Simple3DEngine(+0x10f01) [0x561ee4a4cf01]

[TRACE] 2025-06-27 20:27:55.258 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.258 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.300 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.300 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.313 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.313 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.326 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.326 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.343 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.344 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.360 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.360 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.375 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.375 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.392 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.392 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.409 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.409 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.425 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.425 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.442 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.442 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.459 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.459 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.475 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.475 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.492 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.493 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.509 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.509 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.528 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.529 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.546 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.546 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.571 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.571 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.587 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.587 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.607 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.607 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.623 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.623 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.639 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.639 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.656 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.656 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.675 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.675 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.690 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.690 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.707 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.707 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.724 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.725 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.740 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.740 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.757 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.757 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.773 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.774 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.790 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.790 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.807 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.807 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.825 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.825 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.841 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.841 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.857 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.858 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.874 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.874 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.891 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.891 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.907 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.907 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.924 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.924 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.941 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.941 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.958 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.958 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.976 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.976 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:55.992 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:55.992 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.008 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.008 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.025 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.026 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.041 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.041 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.072 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.087 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.088 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.105 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.105 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.121 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.121 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.138 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.138 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.155 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.155 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.171 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.172 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.188 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.188 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.205 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.205 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.221 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.221 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.238 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.238 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.255 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.271 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.272 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.288 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.288 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.305 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.305 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.321 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.321 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
[TRACE] 2025-06-27 20:27:56.340 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:15) Drawing mesh with VAO: 1, indices: 3
[TRACE] 2025-06-27 20:27:56.340 (/home/<USER>/test_google_cli/test1/src/mesh.cpp:31) Mesh drawn successfully
