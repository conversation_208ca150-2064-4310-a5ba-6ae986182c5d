cmake_minimum_required(VERSION 3.10)
project(Simple3DEngine C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(glfw3 REQUIRED)
find_package(OpenGL REQUIRED)
find_package(glm QUIET)

# If GLM is not found via find_package, try pkg-config
if(NOT glm_FOUND)
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(GLM glm)
    endif()
endif()

include_directories(include json_parser/include gltf_parser/include utils/include external/include)

# Create GLAD library
add_library(glad external/src/glad.c)
target_include_directories(glad PUBLIC external/include)

add_executable(Simple3DEngine src/main.cpp src/shader.cpp src/camera.cpp src/mesh.cpp src/animation.cpp src/model_controller.cpp json_parser/src/json.cpp gltf_parser/src/gltf.cpp utils/src/base_encoding.cpp utils/src/directory_utils.cpp utils/src/logger.cpp utils/src/error_handler.cpp)
# Link libraries
target_link_libraries(Simple3DEngine
    glad
    glfw
    OpenGL::GL
    ${CMAKE_DL_LIBS}  # For dynamic loading on Linux
)

# Add GLM if found
if(glm_FOUND)
    target_link_libraries(Simple3DEngine glm::glm)
elseif(GLM_FOUND)
    target_include_directories(Simple3DEngine PRIVATE ${GLM_INCLUDE_DIRS})
endif()

# Set compiler flags for better error reporting
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(Simple3DEngine PRIVATE -Wall -Wextra -Wpedantic)
endif()
