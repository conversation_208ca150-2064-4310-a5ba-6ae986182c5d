cmake_minimum_required(VERSION 3.3)
project(Simple3DEngine CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(glfw3 CONFIG REQUIRED)

include_directories(include json_parser/include gltf_parser/include utils/include external/glad/include)

add_executable(Simple3DEngine src/main.cpp src/shader.cpp src/camera.cpp src/mesh.cpp json_parser/src/json.cpp gltf_parser/src/gltf.cpp utils/src/base_encoding.cpp utils/src/directory_utils.cpp utils/src/logger.cpp utils/src/error_handler.cpp external/glad/src/glad.c)
target_link_libraries(Simple3DEngine glfw OpenGL)
