[2025-06-27 16:15:04] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:15:04] [INFO] GLFW window created successfully.
[2025-06-27 16:15:04] [INFO] Shader program compiled.
[2025-06-27 16:15:04] [INFO] glTF file parsed successfully.
[2025-06-27 16:15:04] [INFO] Mesh loaded from glTF.
[2025-06-27 16:15:04] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:19:01] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:19:01] [INFO] GLFW window created successfully.
[2025-06-27 16:19:01] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:19:01] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:19:01] [INFO] Shader program compiled.
[2025-06-27 16:19:01] [INFO] glTF file parsed successfully.
[2025-06-27 16:19:01] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 16:19:01] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 16:19:01] [INFO] Mesh loaded from glTF.
[2025-06-27 16:19:07] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:19:56] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:19:56] [INFO] GLFW window created successfully.
[2025-06-27 16:19:56] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:19:56] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:19:56] [INFO] Shader program compiled.
[2025-06-27 16:19:56] [INFO] glTF file parsed successfully.
[2025-06-27 16:19:56] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 16:19:56] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 16:19:56] [INFO] Mesh loaded from glTF.
[2025-06-27 16:19:59] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:20:30] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:20:30] [INFO] GLFW window created successfully.
[2025-06-27 16:20:30] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:20:30] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:20:30] [INFO] Shader program compiled.
[2025-06-27 16:20:30] [INFO] glTF file parsed successfully.
[2025-06-27 16:20:30] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 16:20:30] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 16:20:30] [INFO] Mesh loaded from glTF.
[2025-06-27 16:20:32] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:21:57] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:21:57] [INFO] GLFW window created successfully.
[2025-06-27 16:21:57] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:21:57] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:21:57] [INFO] Shader program compiled.
[2025-06-27 16:21:57] [INFO] glTF file parsed successfully.
[2025-06-27 16:21:57] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 16:21:57] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 16:21:57] [INFO] Mesh loaded from glTF.
[2025-06-27 16:21:59] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:24:10] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:24:11] [INFO] GLFW window created successfully.
[2025-06-27 16:24:11] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:24:11] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:24:11] [INFO] Shader program compiled.
[2025-06-27 16:24:11] [INFO] glTF file parsed successfully.
[2025-06-27 16:24:11] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 16:24:11] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 16:24:11] [INFO] Mesh loaded from glTF.
[2025-06-27 16:24:11] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 16:36:45] [INFO] Starting Simple 3D Engine...
[2025-06-27 16:36:45] [INFO] GLFW window created successfully.
[2025-06-27 16:36:45] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 16:36:45] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 16:36:45] [INFO] Shader program compiled.
[2025-06-27 16:36:45] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:162) Hardcoded cube setup complete.
[2025-06-27 16:36:50] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 18:52:04] [INFO] Starting Simple 3D Engine...
[2025-06-27 18:52:04] [INFO] GLFW window created successfully.
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 18:52:04] [INFO] Shader program compiled.
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:104) glTF file parsed successfully.
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 18:52:04] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:193) Mesh loaded from glTF.
[2025-06-27 18:52:10] [INFO] Terminating GLFW and cleaning up resources.
[2025-06-27 18:55:12] [INFO] Starting Simple 3D Engine...
[2025-06-27 18:55:12] [INFO] GLFW window created successfully.
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:31) Shader files loaded: src/simple.vert, src/simple.frag
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/shader.cpp:78) Shader program created successfully with ID: 3
[2025-06-27 18:55:12] [INFO] Shader program compiled.
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:104) glTF file parsed successfully.
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:46) Mesh VAO, VBO, EBO setup complete.
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/mesh.cpp:12) Mesh created with 3 vertices and 3 indices.
[2025-06-27 18:55:12] [INFO] (/home/<USER>/test_google_cli/test1/src/main.cpp:193) Mesh loaded from glTF.
[2025-06-27 18:55:17] [INFO] Terminating GLFW and cleaning up resources.
