#ifndef MESH_H
#define MESH_H

#include <GL/gl.h>
#include <vector>
#include "camera.h" // For Vec3

struct Vertex {
    glm::vec3 Position;
    glm::vec3 Normal;
    float TexCoords[2];
};

class Mesh {
public:
    std::vector<Vertex> vertices;
    std::vector<GLuint> indices;

    Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices);
    void Draw();

private:
    GLuint VAO, VBO, EBO;
    void setupMesh();
};

#endif
