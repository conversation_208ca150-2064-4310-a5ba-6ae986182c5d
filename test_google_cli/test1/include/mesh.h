#ifndef MESH_H
#define MESH_H

#include <glad/glad.h>
#include <vector>
#include <glm/glm.hpp>

struct Vertex {
    glm::vec3 Position;
    glm::vec3 Normal;
    glm::vec2 TexCoords;
    glm::vec4 BoneWeights;  // Weights for up to 4 bones
    glm::ivec4 BoneIndices; // Indices of up to 4 bones

    // Constructor for easy initialization
    Vertex() : Position(0.0f), Normal(0.0f), TexCoords(0.0f), BoneWeights(0.0f), BoneIndices(0) {}
    Vertex(glm::vec3 pos, glm::vec3 norm = glm::vec3(0.0f), glm::vec2 tex = glm::vec2(0.0f))
        : Position(pos), Normal(norm), TexCoords(tex), BoneWeights(0.0f), BoneIndices(0) {}
};

class Mesh {
public:
    std::vector<Vertex> vertices;
    std::vector<GLuint> indices;

    Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices);
    void Draw();

private:
    GLuint VAO, VBO, EBO;
    void setupMesh();
};

#endif
