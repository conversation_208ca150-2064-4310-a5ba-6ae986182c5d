#ifndef MESH_H
#define MESH_H

#include <glad/glad.h>
#include <vector>
#include <memory>
#include <string>
#include <glm/glm.hpp>

// Forward declaration
class Texture;
class Shader;

struct Vertex {
    glm::vec3 Position;
    glm::vec3 Normal;
    glm::vec2 TexCoords;
    glm::vec4 BoneWeights;  // Weights for up to 4 bones
    glm::ivec4 BoneIndices; // Indices of up to 4 bones

    // Constructor for easy initialization
    Vertex() : Position(0.0f), Normal(0.0f), TexCoords(0.0f), BoneWeights(0.0f), BoneIndices(0) {}
    Vertex(glm::vec3 pos, glm::vec3 norm = glm::vec3(0.0f), glm::vec2 tex = glm::vec2(0.0f))
        : Position(pos), Normal(norm), TexCoords(tex), BoneWeights(0.0f), BoneIndices(0) {}
};

enum class TextureType {
    DIFFUSE,
    SPECULAR,
    NORMAL,
    HEIGHT,
    AMBIENT,
    EMISSIVE,
    METALLIC,
    ROUGHNESS,
    OCCLUSION
};

struct MeshTexture {
    std::shared_ptr<Texture> texture;
    TextureType type;
    std::string typeName;

    MeshTexture(std::shared_ptr<Texture> tex, TextureType t, const std::string& name)
        : texture(tex), type(t), typeName(name) {}
};

class Mesh {
public:
    std::vector<Vertex> vertices;
    std::vector<GLuint> indices;
    std::vector<MeshTexture> textures;

    // Constructors
    Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices);
    Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices, std::vector<MeshTexture> textures);

    // Destructor
    ~Mesh();

    // Rendering
    void Draw(Shader* shader = nullptr);
    void DrawInstanced(unsigned int instanceCount, Shader* shader = nullptr);

    // Texture management
    void addTexture(std::shared_ptr<Texture> texture, TextureType type, const std::string& typeName = "");
    void removeTexture(TextureType type);
    void clearTextures();
    std::shared_ptr<Texture> getTexture(TextureType type) const;
    bool hasTexture(TextureType type) const;
    size_t getTextureCount() const { return textures.size(); }

    // Mesh properties
    size_t getVertexCount() const { return vertices.size(); }
    size_t getIndexCount() const { return indices.size(); }
    GLuint getVAO() const { return VAO; }

    // Utility
    void updateMesh(); // Call this after modifying vertices/indices
    bool isValid() const { return VAO != 0; }

private:
    GLuint VAO, VBO, EBO;
    bool meshSetup;

    void setupMesh();
    void bindTextures(Shader* shader);
    void cleanup();
};

#endif
