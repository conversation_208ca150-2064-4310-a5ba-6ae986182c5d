#ifndef ANIMATION_H
#define ANIMATION_H

#include <vector>
#include <map>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include "gltf.h"

class AnimationManager {
public:
    struct NodeTransform {
        glm::vec3 translation = glm::vec3(0.0f);
        glm::quat rotation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
        glm::vec3 scale = glm::vec3(1.0f);
        
        glm::mat4 getMatrix() const {
            glm::mat4 T = glm::translate(glm::mat4(1.0f), translation);
            glm::mat4 R = glm::mat4_cast(rotation);
            glm::mat4 S = glm::scale(glm::mat4(1.0f), scale);
            return T * R * S;
        }
    };

    struct AnimationState {
        int animationIndex = -1;
        float currentTime = 0.0f;
        bool playing = false;
        bool looping = true;
        float duration = 0.0f;
    };

private:
    const Gltf::GltfModel* model;
    std::vector<NodeTransform> nodeTransforms;
    AnimationState currentAnimation;
    std::vector<glm::mat4> boneMatrices;
    std::vector<glm::mat4> inverseBindMatrices;

public:
    AnimationManager(const Gltf::GltfModel* gltfModel);
    
    void playAnimation(int animationIndex, bool loop = true);
    void stopAnimation();
    void updateAnimation(float deltaTime);
    
    const std::vector<NodeTransform>& getNodeTransforms() const { return nodeTransforms; }
    const std::vector<glm::mat4>& getBoneMatrices() const { return boneMatrices; }
    
    glm::mat4 getNodeMatrix(int nodeIndex) const;
    void updateBoneMatrices(int skinIndex);

private:
    void initializeNodeTransforms();
    void calculateAnimationDuration(int animationIndex);
    void calculateGlobalTransforms(int nodeIndex, const glm::mat4& parentTransform, std::vector<glm::mat4>& globalTransforms) const;

    // Interpolation functions
    glm::vec3 interpolateVec3(const std::vector<float>& times, const std::vector<glm::vec3>& values, float time, const std::string& interpolation);
    glm::quat interpolateQuat(const std::vector<float>& times, const std::vector<glm::quat>& values, float time, const std::string& interpolation);
    
    // Helper functions for keyframe data extraction
    std::vector<float> extractTimeData(const Gltf::Accessor& accessor) const;
    std::vector<glm::vec3> extractVec3Data(const Gltf::Accessor& accessor) const;
    std::vector<glm::quat> extractQuatData(const Gltf::Accessor& accessor) const;
    
    // Utility functions
    size_t findKeyframeIndex(const std::vector<float>& times, float time) const;
    float clamp(float value, float min, float max) const;
};

#endif // ANIMATION_H
