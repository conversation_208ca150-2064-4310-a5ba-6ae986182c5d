#ifndef MODEL_H
#define MODEL_H

#include <vector>
#include <memory>
#include <string>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Forward declarations
class Mesh;
class Texture;
class Shader;
class AnimationManager;
struct GLTFModel;

class Model {
public:
    // Constructors
    Model();
    Model(const std::string& path);
    ~Model();

    // Non-copyable but movable
    Model(const Model&) = delete;
    Model& operator=(const Model&) = delete;
    Model(Model&& other) noexcept;
    Model& operator=(Model&& other) noexcept;

    // Loading
    bool loadFromFile(const std::string& path);
    bool loadFromGLTF(const GLTFModel& gltfModel);
    void clear();

    // Rendering
    void draw(Shader* shader = nullptr);
    void drawInstanced(unsigned int instanceCount, Shader* shader = nullptr);

    // Transformation
    void setPosition(const glm::vec3& position);
    void setRotation(const glm::vec3& rotation);
    void setScale(const glm::vec3& scale);
    void setTransform(const glm::mat4& transform);
    
    glm::vec3 getPosition() const { return position; }
    glm::vec3 getRotation() const { return rotation; }
    glm::vec3 getScale() const { return scale; }
    glm::mat4 getModelMatrix() const;
    
    // Translation methods
    void translate(const glm::vec3& offset);
    void rotate(float angle, const glm::vec3& axis);
    void scaleBy(const glm::vec3& factor);

    // Mesh management
    void addMesh(std::shared_ptr<Mesh> mesh);
    void removeMesh(size_t index);
    std::shared_ptr<Mesh> getMesh(size_t index) const;
    size_t getMeshCount() const { return meshes.size(); }
    void clearMeshes();

    // Texture management
    bool addTexture(const std::string& texturePath, size_t meshIndex = 0);
    bool addTexture(std::shared_ptr<Texture> texture, size_t meshIndex = 0);
    void removeTextures(size_t meshIndex = 0);

    // Animation support
    void setAnimationManager(std::shared_ptr<AnimationManager> animManager);
    std::shared_ptr<AnimationManager> getAnimationManager() const { return animationManager; }
    bool hasAnimations() const;
    void updateAnimation(float deltaTime);

    // Properties
    void setBoundingRadius(float radius) { boundingRadius = radius; }
    float getBoundingRadius() const { return boundingRadius; }
    void setVisible(bool visible) { isVisible = visible; }
    bool getVisible() const { return isVisible; }
    
    // Utility
    bool isLoaded() const { return !meshes.empty(); }
    const std::string& getFilePath() const { return filePath; }
    
    // Bounding box calculation
    void calculateBoundingBox();
    glm::vec3 getBoundingBoxMin() const { return boundingBoxMin; }
    glm::vec3 getBoundingBoxMax() const { return boundingBoxMax; }

private:
    // Meshes
    std::vector<std::shared_ptr<Mesh>> meshes;
    
    // Transformation
    glm::vec3 position;
    glm::vec3 rotation;
    glm::vec3 scale;
    mutable glm::mat4 modelMatrix;
    mutable bool matrixNeedsUpdate;
    
    // Animation
    std::shared_ptr<AnimationManager> animationManager;
    
    // Properties
    float boundingRadius;
    bool isVisible;
    std::string filePath;
    
    // Bounding box
    glm::vec3 boundingBoxMin;
    glm::vec3 boundingBoxMax;
    bool boundingBoxCalculated;
    
    // Helper methods
    void updateModelMatrix() const;
    void markMatrixDirty();
    bool loadTextures(const std::string& directory);
    std::string extractDirectory(const std::string& path) const;
};

#endif // MODEL_H
