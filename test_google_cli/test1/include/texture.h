#ifndef TEXTURE_H
#define TEXTURE_H

#include <glad/glad.h>
#include <string>
#include <unordered_map>
#include <memory>

enum class TextureFormat {
    RGB = GL_RGB,
    RGBA = GL_RGBA,
    RED = GL_RED,
    RG = GL_RG
};

enum class TextureWrap {
    REPEAT = GL_REPEAT,
    MIRRORED_REPEAT = GL_MIRRORED_REPEAT,
    CLAMP_TO_EDGE = GL_CLAMP_TO_EDGE,
    CLAMP_TO_BORDER = GL_CLAMP_TO_BORDER
};

enum class TextureFilter {
    NEAREST = GL_NEAREST,
    LINEAR = GL_LINEAR,
    NEAREST_MIPMAP_NEAREST = GL_NEAREST_MIPMAP_NEAREST,
    LINEAR_MIPMAP_NEAREST = GL_LINEAR_MIPMAP_NEAREST,
    NEAREST_MIPMAP_LINEAR = GL_NEAREST_MIPMAP_LINEAR,
    LINEAR_MIPMAP_LINEAR = GL_LINEAR_MIPMAP_LINEAR
};

class Texture {
public:
    Texture();
    ~Texture();

    // Non-copyable but movable
    Texture(const Texture&) = delete;
    Texture& operator=(const Texture&) = delete;
    Texture(Texture&& other) noexcept;
    Texture& operator=(Texture&& other) noexcept;

    // Loading methods
    bool loadFromFile(const std::string& filePath, bool flipVertically = true);
    bool loadFromMemory(const unsigned char* data, int width, int height, TextureFormat format);
    
    // OpenGL operations
    void bind(unsigned int textureUnit = 0) const;
    void unbind() const;
    
    // Configuration
    void setWrapMode(TextureWrap wrapS, TextureWrap wrapT);
    void setFilterMode(TextureFilter minFilter, TextureFilter magFilter);
    void generateMipmaps();
    
    // Getters
    unsigned int getID() const { return textureID; }
    int getWidth() const { return width; }
    int getHeight() const { return height; }
    int getChannels() const { return channels; }
    TextureFormat getFormat() const { return format; }
    const std::string& getFilePath() const { return filePath; }
    bool isValid() const { return textureID != 0; }

    // Static texture cache management
    static std::shared_ptr<Texture> loadCached(const std::string& filePath, bool flipVertically = true);
    static void clearCache();
    static size_t getCacheSize();

private:
    unsigned int textureID;
    int width, height, channels;
    TextureFormat format;
    std::string filePath;

    // Static cache for texture sharing
    static std::unordered_map<std::string, std::weak_ptr<Texture>> textureCache;

    // Helper methods
    void cleanup();
    bool loadImageData(const std::string& filePath, bool flipVertically);
    GLenum getInternalFormat(int channels) const;
    GLenum getDataFormat(int channels) const;
};

#endif // TEXTURE_H
