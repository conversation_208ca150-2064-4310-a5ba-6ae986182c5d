#ifndef MODEL_CONTROLLER_H
#define MODEL_CONTROLLER_H

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <vector>
#include <memory>

// Forward declarations
struct GLFWwindow;
class Camera;

// Structure to represent an obstacle in 3D space
struct Obstacle {
    glm::vec3 position;
    glm::vec3 size;        // Half-extents for box collision
    float radius;          // For sphere collision (if radius > 0, use sphere, otherwise use box)
    
    Obstacle(glm::vec3 pos, glm::vec3 boxSize) : position(pos), size(boxSize), radius(0.0f) {}
    Obstacle(glm::vec3 pos, float sphereRadius) : position(pos), size(glm::vec3(0.0f)), radius(sphereRadius) {}
};

// Structure to represent a draggable model
struct DraggableModel {
    glm::vec3 position;
    glm::vec3 scale;
    glm::vec3 rotation;
    glm::mat4 modelMatrix;
    bool isSelected;
    bool isDragging;
    float boundingRadius;  // For collision detection
    
    DraggableModel() : 
        position(0.0f), 
        scale(1.0f), 
        rotation(0.0f), 
        modelMatrix(1.0f),
        isSelected(false),
        isDragging(false),
        boundingRadius(1.0f) {}
};

class ModelController {
public:
    ModelController(Camera* camera, int screenWidth, int screenHeight);
    ~ModelController();
    
    // Model management
    void addModel(const glm::vec3& position = glm::vec3(0.0f), 
                  const glm::vec3& scale = glm::vec3(1.0f),
                  float boundingRadius = 1.0f);
    void removeModel(int modelIndex);
    DraggableModel* getModel(int index);
    size_t getModelCount() const { return models.size(); }
    
    // Obstacle management
    void addBoxObstacle(const glm::vec3& position, const glm::vec3& size);
    void addSphereObstacle(const glm::vec3& position, float radius);
    void clearObstacles();
    const std::vector<Obstacle>& getObstacles() const { return obstacles; }
    
    // Input handling
    void handleMouseButton(GLFWwindow* window, int button, int action, int mods);
    void handleMouseMove(GLFWwindow* window, double xpos, double ypos);
    void handleKeyboard(GLFWwindow* window, float deltaTime);
    
    // Update and rendering
    void update(float deltaTime);
    glm::mat4 getModelMatrix(int modelIndex) const;
    
    // Utility functions
    void setScreenSize(int width, int height);
    glm::vec3 screenToWorld(double mouseX, double mouseY, float depth = 0.0f);
    bool rayIntersectsSphere(const glm::vec3& rayOrigin, const glm::vec3& rayDirection, 
                           const glm::vec3& sphereCenter, float sphereRadius, float& distance);
    
    // Collision detection
    bool checkCollision(const glm::vec3& position, float radius) const;
    glm::vec3 resolveCollision(const glm::vec3& oldPosition, const glm::vec3& newPosition, float radius) const;
    
    // Selection and dragging
    int getSelectedModelIndex() const { return selectedModelIndex; }
    bool isDragging() const { return dragging; }
    
private:
    Camera* camera;
    int screenWidth, screenHeight;
    
    std::vector<DraggableModel> models;
    std::vector<Obstacle> obstacles;
    
    // Mouse interaction state
    int selectedModelIndex;
    bool dragging;
    glm::vec3 dragOffset;
    glm::vec2 lastMousePos;
    glm::vec3 dragPlaneNormal;
    float dragPlaneDistance;
    
    // Private helper methods
    void updateModelMatrix(DraggableModel& model);
    int pickModel(double mouseX, double mouseY);
    void startDragging(int modelIndex, double mouseX, double mouseY);
    void updateDragging(double mouseX, double mouseY);
    void stopDragging();
    glm::vec3 intersectRayPlane(const glm::vec3& rayOrigin, const glm::vec3& rayDirection,
                               const glm::vec3& planeNormal, float planeDistance);
};

#endif // MODEL_CONTROLLER_H
