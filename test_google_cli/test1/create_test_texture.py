#!/usr/bin/env python3
"""
Create a simple test texture for the 3D model
"""

from PIL import Image
import numpy as np

def create_test_texture():
    # Create a 256x256 texture
    width, height = 256, 256
    
    # Create a colorful pattern
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Create a checkerboard pattern with colors
    for y in range(height):
        for x in range(width):
            # Create checkerboard pattern
            checker_size = 32
            checker_x = (x // checker_size) % 2
            checker_y = (y // checker_size) % 2
            
            if (checker_x + checker_y) % 2 == 0:
                # Light blue squares
                image[y, x] = [100, 150, 255]
            else:
                # Orange squares
                image[y, x] = [255, 150, 50]
    
    # Convert to PIL Image and save
    pil_image = Image.fromarray(image, 'RGB')
    pil_image.save('CesiumMan_img0.jpg', 'JPEG', quality=90)
    print("Created test texture: CesiumMan_img0.jpg")

if __name__ == "__main__":
    create_test_texture()
