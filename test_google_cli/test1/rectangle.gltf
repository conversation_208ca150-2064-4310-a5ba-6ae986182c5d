{"scene": 0, "scenes": [{"nodes": [0, 1]}], "nodes": [{"skin": 0, "mesh": 0}, {"children": [2]}, {"translation": [0.0, 1.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0]}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "JOINTS_0": 2, "WEIGHTS_0": 3}, "indices": 0}]}], "skins": [{"inverseBindMatrices": 4, "joints": [1, 2]}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 2, "path": "rotation"}}], "samplers": [{"input": 5, "interpolation": "LINEAR", "output": 6}]}], "buffers": [{"uri": "data:application/gltf-buffer;base64,AAABAAMAAAADAAIAAgADAAUAAgAFAAQABAAFAAcABAAHAAYABgAHAAkABgAJAAgAAAAAvwAAAAAAAAAAAAAAPwAAAAAAAAAAAAAAvwAAAD8AAAAAAAAAPwAAAD8AAAAAAAAAvwAAgD8AAAAAAAAAPwAAgD8AAAAAAAAAvwAAwD8AAAAAAAAAPwAAwD8AAAAAAAAAvwAAAEAAAAAAAAAAPwAAAEAAAAAA", "byteLength": 168}, {"uri": "data:application/gltf-buffer;base64,AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAgD8AAAAAAAAAAAAAAAAAAIA/AAAAAAAAAAAAAAAAAABAPwAAgD4AAAAAAAAAAAAAQD8AAIA+AAAAAAAAAAAAAAA/AAAAPwAAAAAAAAAAAAAAPwAAAD8AAAAAAAAAAAAAgD4AAEA/AAAAAAAAAAAAAIA+AABAPwAAAAAAAAAAAAAAAAAAgD8AAAAAAAAAAAAAAAAAAIA/AAAAAAAAAAA=", "byteLength": 320}, {"uri": "data:application/gltf-buffer;base64,AACAPwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAAAAAAAAAAAAAAAAAAAAAgD8AAAAAAAAAAAAAAAAAAAAAAACAPwAAgD8AAAAAAAAAAAAAAAAAAAAAAACAPwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAAAAAAAAAAIC/AAAAAAAAgD8=", "byteLength": 128}, {"uri": "data:application/gltf-buffer;base64,AAAAAAAAAD8AAIA/AADAPwAAAEAAACBAAABAQAAAYEAAAIBAAACQQAAAoEAAALBAAAAAAAAAAAAAAAAAAACAPwAAAAAAAAAAkxjEPkSLbD8AAAAAAAAAAPT9ND/0/TQ/AAAAAAAAAAD0/TQ/9P00PwAAAAAAAAAAkxjEPkSLbD8AAAAAAAAAAAAAAAAAAIA/AAAAAAAAAAAAAAAAAACAPwAAAAAAAAAAkxjEvkSLbD8AAAAAAAAAAPT9NL/0/TQ/AAAAAAAAAAD0/TS/9P00PwAAAAAAAAAAkxjEvkSLbD8AAAAAAAAAAAAAAAAAAIA/", "byteLength": 240}], "bufferViews": [{"buffer": 0, "byteLength": 48, "target": 34963}, {"buffer": 0, "byteOffset": 48, "byteLength": 120, "target": 34962}, {"buffer": 1, "byteLength": 320, "byteStride": 16}, {"buffer": 2, "byteLength": 128}, {"buffer": 3, "byteLength": 240}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 24, "type": "SCALAR"}, {"bufferView": 1, "componentType": 5126, "count": 10, "type": "VEC3", "max": [0.5, 2.0, 0.0], "min": [-0.5, 0.0, 0.0]}, {"bufferView": 2, "componentType": 5123, "count": 10, "type": "VEC4"}, {"bufferView": 2, "byteOffset": 160, "componentType": 5126, "count": 10, "type": "VEC4"}, {"bufferView": 3, "componentType": 5126, "count": 2, "type": "MAT4"}, {"bufferView": 4, "componentType": 5126, "count": 12, "type": "SCALAR", "max": [5.5], "min": [0.0]}, {"bufferView": 4, "byteOffset": 48, "componentType": 5126, "count": 12, "type": "VEC4", "max": [0.0, 0.0, 0.707, 1.0], "min": [0.0, 0.0, -0.707, 0.707]}], "asset": {"version": "2.0"}}