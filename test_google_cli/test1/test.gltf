{"asset": {"version": "2.0", "generator": "Gemini CLI"}, "scenes": [{"name": "Triangle Scene", "nodes": [0]}], "scene": 0, "nodes": [{"name": "Triangle", "mesh": 0}], "meshes": [{"name": "<PERSON><PERSON><PERSON>", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1}, "indices": 2, "mode": 4}]}], "buffers": [{"uri": "data:application/octet-stream;base64,AAAAPwAAAEAAAAAAAEAAQAAAAAAAIA/AAAAgD8AAAAAAACAPwAAAAAAAIA/AAAAgD8AAAAAAACAPwAAAAAAAIA/AAAAAAEAAg==", "byteLength": 78}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 36, "target": 34962}, {"buffer": 0, "byteOffset": 36, "byteLength": 36, "target": 34962}, {"buffer": 0, "byteOffset": 72, "byteLength": 6, "target": 34963}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5126, "count": 3, "type": "VEC3", "max": [0.5, 0.5, 0.0], "min": [-0.5, -0.5, 0.0]}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 3, "type": "VEC3"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5123, "count": 3, "type": "SCALAR"}]}