#include "model.h"
#include "mesh.h"
#include "texture.h"
#include "shader.h"
#include "animation.h"
#include "gltf.h"
#include "logger.h"
#include <algorithm>
#include <filesystem>

Model::Model() 
    : position(0.0f), rotation(0.0f), scale(1.0f), modelMatrix(1.0f), matrixNeedsUpdate(true),
      boundingRadius(1.0f), isVisible(true), boundingBoxMin(0.0f), boundingBoxMax(0.0f), 
      boundingBoxCalculated(false) {
    LOG_INFO("Model created");
}

Model::Model(const std::string& path) : Model() {
    loadFromFile(path);
}

Model::~Model() {
    clear();
    LOG_INFO("Model destroyed");
}

Model::Model(Model&& other) noexcept 
    : meshes(std::move(other.meshes)), position(other.position), rotation(other.rotation),
      scale(other.scale), modelMatrix(other.modelMatrix), matrixNeedsUpdate(other.matrixNeedsUpdate),
      animationManager(std::move(other.animationManager)), boundingRadius(other.boundingRadius),
      isVisible(other.isVisible), filePath(std::move(other.filePath)),
      boundingBoxMin(other.boundingBoxMin), boundingBoxMax(other.boundingBoxMax),
      boundingBoxCalculated(other.boundingBoxCalculated) {
    
    other.matrixNeedsUpdate = true;
    other.boundingBoxCalculated = false;
}

Model& Model::operator=(Model&& other) noexcept {
    if (this != &other) {
        clear();
        
        meshes = std::move(other.meshes);
        position = other.position;
        rotation = other.rotation;
        scale = other.scale;
        modelMatrix = other.modelMatrix;
        matrixNeedsUpdate = other.matrixNeedsUpdate;
        animationManager = std::move(other.animationManager);
        boundingRadius = other.boundingRadius;
        isVisible = other.isVisible;
        filePath = std::move(other.filePath);
        boundingBoxMin = other.boundingBoxMin;
        boundingBoxMax = other.boundingBoxMax;
        boundingBoxCalculated = other.boundingBoxCalculated;
        
        other.matrixNeedsUpdate = true;
        other.boundingBoxCalculated = false;
    }
    return *this;
}

bool Model::loadFromFile(const std::string& path) {
    clear();
    filePath = path;
    
    // For now, we'll focus on glTF loading since that's what the current system uses
    LOG_INFO("Loading model from file: " + path);
    
    // This would be implemented to load various model formats
    // For now, we'll assume glTF loading is handled externally
    LOG_WARNING("Direct file loading not implemented yet - use loadFromGLTF instead");
    return false;
}

bool Model::loadFromGLTF(const GLTFModel& gltfModel) {
    clear();
    
    LOG_INFO("Loading model from glTF data");
    
    try {
        // Convert glTF meshes to our Mesh objects
        for (const auto& gltfMesh : gltfModel.meshes) {
            // Create vertices from glTF data
            std::vector<Vertex> vertices;
            std::vector<GLuint> indices;
            
            // Convert glTF mesh data to our format
            // This is a simplified conversion - in a real implementation,
            // you'd need to handle all the glTF mesh attributes properly
            
            for (size_t i = 0; i < gltfMesh.vertices.size(); ++i) {
                Vertex vertex;
                if (i < gltfMesh.vertices.size()) {
                    vertex.Position = gltfMesh.vertices[i];
                }
                if (i < gltfMesh.normals.size()) {
                    vertex.Normal = gltfMesh.normals[i];
                }
                if (i < gltfMesh.texCoords.size()) {
                    vertex.TexCoords = gltfMesh.texCoords[i];
                }
                if (i < gltfMesh.boneWeights.size()) {
                    vertex.BoneWeights = gltfMesh.boneWeights[i];
                }
                if (i < gltfMesh.boneIndices.size()) {
                    vertex.BoneIndices = gltfMesh.boneIndices[i];
                }
                vertices.push_back(vertex);
            }
            
            indices = gltfMesh.indices;
            
            auto mesh = std::make_shared<Mesh>(vertices, indices);
            
            // Add texture if available
            if (gltfMesh.textureID != 0) {
                // Create a texture wrapper for the existing OpenGL texture
                // This is a temporary solution - ideally we'd load textures through our Texture class
                LOG_INFO("Mesh has texture ID: " + std::to_string(gltfMesh.textureID));
            }
            
            addMesh(mesh);
        }
        
        calculateBoundingBox();
        LOG_INFO("Successfully loaded glTF model with " + std::to_string(meshes.size()) + " meshes");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to load glTF model: " + std::string(e.what()));
        clear();
        return false;
    }
}

void Model::clear() {
    meshes.clear();
    animationManager.reset();
    filePath.clear();
    boundingBoxCalculated = false;
    markMatrixDirty();
    LOG_INFO("Model cleared");
}

void Model::draw(Shader* shader) {
    if (!isVisible || meshes.empty()) {
        return;
    }
    
    // Set model matrix in shader if provided
    if (shader) {
        glm::mat4 modelMat = getModelMatrix();
        shader->setMat4("model", glm::value_ptr(modelMat));
    }
    
    // Draw all meshes
    for (auto& mesh : meshes) {
        if (mesh) {
            mesh->Draw(shader);
        }
    }
}

void Model::drawInstanced(unsigned int instanceCount, Shader* shader) {
    if (!isVisible || meshes.empty()) {
        return;
    }
    
    // Set model matrix in shader if provided
    if (shader) {
        glm::mat4 modelMat = getModelMatrix();
        shader->setMat4("model", glm::value_ptr(modelMat));
    }
    
    // Draw all meshes instanced
    for (auto& mesh : meshes) {
        if (mesh) {
            mesh->DrawInstanced(instanceCount, shader);
        }
    }
}

void Model::setPosition(const glm::vec3& position) {
    this->position = position;
    markMatrixDirty();
}

void Model::setRotation(const glm::vec3& rotation) {
    this->rotation = rotation;
    markMatrixDirty();
}

void Model::setScale(const glm::vec3& scale) {
    this->scale = scale;
    markMatrixDirty();
}

void Model::setTransform(const glm::mat4& transform) {
    this->modelMatrix = transform;
    matrixNeedsUpdate = false;
    
    // Extract position, rotation, and scale from matrix if needed
    // This is complex and would require matrix decomposition
    LOG_INFO("Model transform set directly");
}

glm::mat4 Model::getModelMatrix() const {
    if (matrixNeedsUpdate) {
        updateModelMatrix();
    }
    return modelMatrix;
}

void Model::translate(const glm::vec3& offset) {
    position += offset;
    markMatrixDirty();
}

void Model::rotate(float angle, const glm::vec3& axis) {
    // This is a simplified rotation - for proper rotation accumulation,
    // you'd want to use quaternions
    glm::mat4 rotationMatrix = glm::rotate(glm::mat4(1.0f), angle, axis);
    // Apply rotation to current rotation (simplified)
    markMatrixDirty();
}

void Model::scaleBy(const glm::vec3& factor) {
    scale *= factor;
    markMatrixDirty();
}

void Model::addMesh(std::shared_ptr<Mesh> mesh) {
    if (mesh) {
        meshes.push_back(mesh);
        boundingBoxCalculated = false;
        LOG_INFO("Added mesh to model (total: " + std::to_string(meshes.size()) + ")");
    }
}

void Model::removeMesh(size_t index) {
    if (index < meshes.size()) {
        meshes.erase(meshes.begin() + index);
        boundingBoxCalculated = false;
        LOG_INFO("Removed mesh from model (remaining: " + std::to_string(meshes.size()) + ")");
    }
}

std::shared_ptr<Mesh> Model::getMesh(size_t index) const {
    if (index < meshes.size()) {
        return meshes[index];
    }
    return nullptr;
}

void Model::clearMeshes() {
    meshes.clear();
    boundingBoxCalculated = false;
    LOG_INFO("Cleared all meshes from model");
}

bool Model::addTexture(const std::string& texturePath, size_t meshIndex) {
    if (meshIndex >= meshes.size()) {
        LOG_ERROR("Invalid mesh index for texture addition: " + std::to_string(meshIndex));
        return false;
    }
    
    auto texture = Texture::loadCached(texturePath);
    if (!texture) {
        LOG_ERROR("Failed to load texture: " + texturePath);
        return false;
    }
    
    meshes[meshIndex]->addTexture(texture, TextureType::DIFFUSE, "texture_diffuse");
    LOG_INFO("Added texture to mesh " + std::to_string(meshIndex) + ": " + texturePath);
    return true;
}

bool Model::addTexture(std::shared_ptr<Texture> texture, size_t meshIndex) {
    if (meshIndex >= meshes.size()) {
        LOG_ERROR("Invalid mesh index for texture addition: " + std::to_string(meshIndex));
        return false;
    }
    
    if (!texture || !texture->isValid()) {
        LOG_ERROR("Invalid texture provided");
        return false;
    }
    
    meshes[meshIndex]->addTexture(texture, TextureType::DIFFUSE, "texture_diffuse");
    LOG_INFO("Added texture to mesh " + std::to_string(meshIndex));
    return true;
}

void Model::removeTextures(size_t meshIndex) {
    if (meshIndex < meshes.size()) {
        meshes[meshIndex]->clearTextures();
        LOG_INFO("Removed textures from mesh " + std::to_string(meshIndex));
    }
}

void Model::setAnimationManager(std::shared_ptr<AnimationManager> animManager) {
    animationManager = animManager;
    LOG_INFO("Animation manager set for model");
}

bool Model::hasAnimations() const {
    return animationManager != nullptr;
}

void Model::updateAnimation(float deltaTime) {
    if (animationManager) {
        animationManager->updateAnimation(deltaTime);
    }
}

void Model::calculateBoundingBox() {
    if (meshes.empty()) {
        boundingBoxMin = glm::vec3(0.0f);
        boundingBoxMax = glm::vec3(0.0f);
        boundingBoxCalculated = true;
        return;
    }

    bool first = true;
    for (const auto& mesh : meshes) {
        if (!mesh) continue;

        for (const auto& vertex : mesh->vertices) {
            if (first) {
                boundingBoxMin = vertex.Position;
                boundingBoxMax = vertex.Position;
                first = false;
            } else {
                boundingBoxMin = glm::min(boundingBoxMin, vertex.Position);
                boundingBoxMax = glm::max(boundingBoxMax, vertex.Position);
            }
        }
    }

    // Calculate bounding radius
    glm::vec3 center = (boundingBoxMin + boundingBoxMax) * 0.5f;
    float maxDistance = 0.0f;

    for (const auto& mesh : meshes) {
        if (!mesh) continue;

        for (const auto& vertex : mesh->vertices) {
            float distance = glm::length(vertex.Position - center);
            maxDistance = std::max(maxDistance, distance);
        }
    }

    boundingRadius = maxDistance;
    boundingBoxCalculated = true;

    LOG_INFO("Calculated bounding box: min(" + std::to_string(boundingBoxMin.x) + ", " +
             std::to_string(boundingBoxMin.y) + ", " + std::to_string(boundingBoxMin.z) +
             ") max(" + std::to_string(boundingBoxMax.x) + ", " +
             std::to_string(boundingBoxMax.y) + ", " + std::to_string(boundingBoxMax.z) +
             ") radius: " + std::to_string(boundingRadius));
}

void Model::updateModelMatrix() const {
    modelMatrix = glm::mat4(1.0f);
    modelMatrix = glm::translate(modelMatrix, position);
    modelMatrix = glm::rotate(modelMatrix, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    modelMatrix = glm::rotate(modelMatrix, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    modelMatrix = glm::rotate(modelMatrix, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    modelMatrix = glm::scale(modelMatrix, scale);
    matrixNeedsUpdate = false;
}

void Model::markMatrixDirty() {
    matrixNeedsUpdate = true;
}

bool Model::loadTextures(const std::string& directory) {
    // This would load textures from a directory
    // Implementation depends on the specific requirements
    LOG_INFO("Loading textures from directory: " + directory);
    return true;
}

std::string Model::extractDirectory(const std::string& path) const {
    size_t lastSlash = path.find_last_of("/\\");
    if (lastSlash != std::string::npos) {
        return path.substr(0, lastSlash + 1);
    }
    return "";
}
