#include "model.h"
#include "mesh.h"
#include "texture.h"
#include "shader.h"
#include "animation.h"
#include "gltf.h"
#include "logger.h"
#include <algorithm>
#include <filesystem>
#include <cstring>
#include <glm/gtc/type_ptr.hpp>

Model::Model() 
    : position(0.0f), rotation(0.0f), scale(1.0f), modelMatrix(1.0f), matrixNeedsUpdate(true),
      boundingRadius(1.0f), isVisible(true), boundingBoxMin(0.0f), boundingBoxMax(0.0f), 
      boundingBoxCalculated(false) {
    LOG_INFO("Model created");
}

Model::Model(const std::string& path) : Model() {
    loadFromFile(path);
}

Model::~Model() {
    clear();
    LOG_INFO("Model destroyed");
}

Model::Model(Model&& other) noexcept 
    : meshes(std::move(other.meshes)), position(other.position), rotation(other.rotation),
      scale(other.scale), modelMatrix(other.modelMatrix), matrixNeedsUpdate(other.matrixNeedsUpdate),
      animationManager(std::move(other.animationManager)), boundingRadius(other.boundingRadius),
      isVisible(other.isVisible), filePath(std::move(other.filePath)),
      boundingBoxMin(other.boundingBoxMin), boundingBoxMax(other.boundingBoxMax),
      boundingBoxCalculated(other.boundingBoxCalculated) {
    
    other.matrixNeedsUpdate = true;
    other.boundingBoxCalculated = false;
}

Model& Model::operator=(Model&& other) noexcept {
    if (this != &other) {
        clear();
        
        meshes = std::move(other.meshes);
        position = other.position;
        rotation = other.rotation;
        scale = other.scale;
        modelMatrix = other.modelMatrix;
        matrixNeedsUpdate = other.matrixNeedsUpdate;
        animationManager = std::move(other.animationManager);
        boundingRadius = other.boundingRadius;
        isVisible = other.isVisible;
        filePath = std::move(other.filePath);
        boundingBoxMin = other.boundingBoxMin;
        boundingBoxMax = other.boundingBoxMax;
        boundingBoxCalculated = other.boundingBoxCalculated;
        
        other.matrixNeedsUpdate = true;
        other.boundingBoxCalculated = false;
    }
    return *this;
}

bool Model::loadFromFile(const std::string& path) {
    clear();
    filePath = path;
    
    // For now, we'll focus on glTF loading since that's what the current system uses
    LOG_INFO("Loading model from file: " + path);
    
    // This would be implemented to load various model formats
    // For now, we'll assume glTF loading is handled externally
    LOG_WARNING("Direct file loading not implemented yet - use loadFromGLTF instead");
    return false;
}

bool Model::loadFromGLTF(const Gltf::GltfModel& gltfModel) {
    clear();

    LOG_INFO("Loading model from glTF data with " + std::to_string(gltfModel.meshes.size()) + " meshes");

    try {
        // Process each mesh in the glTF model
        for (const auto& gltfMesh : gltfModel.meshes) {
            LOG_INFO("Processing mesh: " + gltfMesh.name + " with " +
                     std::to_string(gltfMesh.primitives.size()) + " primitives");

            // Process each primitive in the mesh
            for (const auto& primitive : gltfMesh.primitives) {
                std::vector<Vertex> vertices;
                std::vector<GLuint> indices;

                // Extract vertex data from glTF primitive
                if (!extractVerticesFromPrimitive(primitive, gltfModel, vertices)) {
                    LOG_WARNING("Failed to extract vertices from primitive, skipping");
                    continue;
                }

                // Extract indices from glTF primitive
                if (!extractIndicesFromPrimitive(primitive, gltfModel, indices)) {
                    LOG_WARNING("Failed to extract indices from primitive, skipping");
                    continue;
                }

                // Create mesh from extracted data
                auto mesh = std::make_shared<Mesh>(vertices, indices);
                addMesh(mesh);

                LOG_INFO("Created mesh with " + std::to_string(vertices.size()) +
                         " vertices and " + std::to_string(indices.size()) + " indices");
            }
        }
        
        calculateBoundingBox();
        LOG_INFO("Successfully loaded glTF model with " + std::to_string(meshes.size()) + " meshes");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("Failed to load glTF model: " + std::string(e.what()));
        clear();
        return false;
    }
}

bool Model::extractVerticesFromPrimitive(const Gltf::Primitive& primitive,
                                          const Gltf::GltfModel& gltfModel,
                                          std::vector<Vertex>& vertices) {
    // Find position accessor
    auto positionIt = primitive.attributes.find("POSITION");
    if (positionIt == primitive.attributes.end()) {
        LOG_ERROR("Primitive missing POSITION attribute");
        return false;
    }

    int positionAccessorIndex = positionIt->second;
    if (positionAccessorIndex >= static_cast<int>(gltfModel.accessors.size())) {
        LOG_ERROR("Invalid position accessor index");
        return false;
    }

    const auto& positionAccessor = gltfModel.accessors[positionAccessorIndex];
    auto positions = extractDataFromAccessor<glm::vec3>(positionAccessor, gltfModel);

    if (positions.empty()) {
        LOG_ERROR("Failed to extract position data");
        return false;
    }

    // Initialize vertices with positions
    vertices.resize(positions.size());
    for (size_t i = 0; i < positions.size(); ++i) {
        vertices[i].Position = positions[i];
        vertices[i].Normal = glm::vec3(0.0f, 1.0f, 0.0f); // Default normal
        vertices[i].TexCoords = glm::vec2(0.0f); // Default texture coordinates
        vertices[i].BoneWeights = glm::vec4(0.0f); // Default bone weights
        vertices[i].BoneIndices = glm::ivec4(0); // Default bone indices
    }

    // Extract normals if available
    auto normalIt = primitive.attributes.find("NORMAL");
    if (normalIt != primitive.attributes.end()) {
        int normalAccessorIndex = normalIt->second;
        if (normalAccessorIndex < static_cast<int>(gltfModel.accessors.size())) {
            const auto& normalAccessor = gltfModel.accessors[normalAccessorIndex];
            auto normals = extractDataFromAccessor<glm::vec3>(normalAccessor, gltfModel);

            for (size_t i = 0; i < std::min(normals.size(), vertices.size()); ++i) {
                vertices[i].Normal = normals[i];
            }
            LOG_INFO("Extracted " + std::to_string(normals.size()) + " normals");
        }
    }

    // Extract texture coordinates if available
    auto texCoordIt = primitive.attributes.find("TEXCOORD_0");
    if (texCoordIt != primitive.attributes.end()) {
        int texCoordAccessorIndex = texCoordIt->second;
        if (texCoordAccessorIndex < static_cast<int>(gltfModel.accessors.size())) {
            const auto& texCoordAccessor = gltfModel.accessors[texCoordAccessorIndex];
            auto texCoords = extractDataFromAccessor<glm::vec2>(texCoordAccessor, gltfModel);

            for (size_t i = 0; i < std::min(texCoords.size(), vertices.size()); ++i) {
                vertices[i].TexCoords = texCoords[i];
            }
            LOG_INFO("Extracted " + std::to_string(texCoords.size()) + " texture coordinates");
        }
    }

    LOG_INFO("Successfully extracted " + std::to_string(vertices.size()) + " vertices");
    return true;
}

bool Model::extractIndicesFromPrimitive(const Gltf::Primitive& primitive,
                                         const Gltf::GltfModel& gltfModel,
                                         std::vector<GLuint>& indices) {
    if (primitive.indices == -1) {
        LOG_WARNING("Primitive has no indices, generating sequential indices");
        // Generate sequential indices for non-indexed geometry
        // This assumes the vertices are already in the correct order for triangles
        return false; // For now, we require indexed geometry
    }

    if (primitive.indices >= static_cast<int>(gltfModel.accessors.size())) {
        LOG_ERROR("Invalid indices accessor index");
        return false;
    }

    const auto& indicesAccessor = gltfModel.accessors[primitive.indices];

    // Handle different index types
    switch (indicesAccessor.componentType) {
        case Gltf::ComponentType::UNSIGNED_SHORT: {
            auto shortIndices = extractDataFromAccessor<uint16_t>(indicesAccessor, gltfModel);
            indices.reserve(shortIndices.size());
            for (uint16_t index : shortIndices) {
                indices.push_back(static_cast<GLuint>(index));
            }
            break;
        }
        case Gltf::ComponentType::UNSIGNED_INT: {
            auto intIndices = extractDataFromAccessor<uint32_t>(indicesAccessor, gltfModel);
            indices.reserve(intIndices.size());
            for (uint32_t index : intIndices) {
                indices.push_back(static_cast<GLuint>(index));
            }
            break;
        }
        case Gltf::ComponentType::UNSIGNED_BYTE: {
            auto byteIndices = extractDataFromAccessor<uint8_t>(indicesAccessor, gltfModel);
            indices.reserve(byteIndices.size());
            for (uint8_t index : byteIndices) {
                indices.push_back(static_cast<GLuint>(index));
            }
            break;
        }
        default:
            LOG_ERROR("Unsupported index component type");
            return false;
    }

    LOG_INFO("Successfully extracted " + std::to_string(indices.size()) + " indices");
    return true;
}

template<typename T>
std::vector<T> Model::extractDataFromAccessor(const Gltf::Accessor& accessor,
                                               const Gltf::GltfModel& gltfModel) {
    std::vector<T> result;

    if (accessor.bufferView >= static_cast<int>(gltfModel.bufferViews.size())) {
        LOG_ERROR("Invalid buffer view index in accessor");
        return result;
    }

    const auto& bufferView = gltfModel.bufferViews[accessor.bufferView];

    if (bufferView.buffer >= static_cast<int>(gltfModel.buffers.size())) {
        LOG_ERROR("Invalid buffer index in buffer view");
        return result;
    }

    const auto& buffer = gltfModel.buffers[bufferView.buffer];

    // Calculate the starting position in the buffer
    size_t offset = bufferView.byteOffset + accessor.byteOffset;

    if (offset >= buffer.data.size()) {
        LOG_ERROR("Buffer offset exceeds buffer size");
        return result;
    }

    // Get component size
    size_t componentSize = 0;
    switch (accessor.componentType) {
        case Gltf::ComponentType::BYTE:
        case Gltf::ComponentType::UNSIGNED_BYTE:
            componentSize = 1;
            break;
        case Gltf::ComponentType::SHORT:
        case Gltf::ComponentType::UNSIGNED_SHORT:
            componentSize = 2;
            break;
        case Gltf::ComponentType::UNSIGNED_INT:
        case Gltf::ComponentType::FLOAT:
            componentSize = 4;
            break;
        default:
            LOG_ERROR("Unsupported component type");
            return result;
    }

    // Get number of components per element
    size_t numComponents = 0;
    switch (accessor.type) {
        case Gltf::Type::SCALAR: numComponents = 1; break;
        case Gltf::Type::VEC2: numComponents = 2; break;
        case Gltf::Type::VEC3: numComponents = 3; break;
        case Gltf::Type::VEC4: numComponents = 4; break;
        case Gltf::Type::MAT2: numComponents = 4; break;
        case Gltf::Type::MAT3: numComponents = 9; break;
        case Gltf::Type::MAT4: numComponents = 16; break;
        default:
            LOG_ERROR("Unsupported accessor type");
            return result;
    }

    size_t elementSize = componentSize * numComponents;
    size_t totalSize = elementSize * accessor.count;

    if (offset + totalSize > buffer.data.size()) {
        LOG_ERROR("Data exceeds buffer bounds");
        return result;
    }

    // Extract the data
    result.resize(accessor.count);
    const unsigned char* dataPtr = buffer.data.data() + offset;

    // Copy data based on component type and target type
    if (accessor.componentType == Gltf::ComponentType::FLOAT && sizeof(T) == elementSize) {
        // Direct copy for float data that matches target size
        std::memcpy(result.data(), dataPtr, totalSize);
    } else {
        // Handle type conversion if needed
        for (size_t i = 0; i < accessor.count; ++i) {
            // This is a simplified version - in a full implementation,
            // you'd handle all type conversions properly
            std::memcpy(&result[i], dataPtr + i * elementSize, std::min(sizeof(T), elementSize));
        }
    }

    return result;
}

void Model::clear() {
    meshes.clear();
    animationManager.reset();
    filePath.clear();
    boundingBoxCalculated = false;
    markMatrixDirty();
    LOG_INFO("Model cleared");
}

void Model::draw(Shader* shader) {
    if (!isVisible || meshes.empty()) {
        LOG_TRACE("Model not visible or no meshes to draw");
        return;
    }

    try {
        // Set model matrix in shader if provided
        if (shader) {
            glm::mat4 modelMat = getModelMatrix();
            shader->setMat4("model", glm::value_ptr(modelMat));
        }

        // Draw all meshes
        for (auto& mesh : meshes) {
            if (mesh && mesh->isValid()) {
                mesh->Draw(shader);
            } else {
                LOG_WARNING("Invalid mesh encountered during drawing");
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in Model::draw: " + std::string(e.what()));
    }
}

void Model::drawInstanced(unsigned int instanceCount, Shader* shader) {
    if (!isVisible || meshes.empty()) {
        return;
    }
    
    // Set model matrix in shader if provided
    if (shader) {
        glm::mat4 modelMat = getModelMatrix();
        shader->setMat4("model", glm::value_ptr(modelMat));
    }
    
    // Draw all meshes instanced
    for (auto& mesh : meshes) {
        if (mesh) {
            mesh->DrawInstanced(instanceCount, shader);
        }
    }
}

void Model::setPosition(const glm::vec3& position) {
    this->position = position;
    markMatrixDirty();
}

void Model::setRotation(const glm::vec3& rotation) {
    this->rotation = rotation;
    markMatrixDirty();
}

void Model::setScale(const glm::vec3& scale) {
    this->scale = scale;
    markMatrixDirty();
}

void Model::setTransform(const glm::mat4& transform) {
    this->modelMatrix = transform;
    matrixNeedsUpdate = false;
    
    // Extract position, rotation, and scale from matrix if needed
    // This is complex and would require matrix decomposition
    LOG_INFO("Model transform set directly");
}

glm::mat4 Model::getModelMatrix() const {
    if (matrixNeedsUpdate) {
        updateModelMatrix();
    }
    return modelMatrix;
}

void Model::translate(const glm::vec3& offset) {
    position += offset;
    markMatrixDirty();
}

void Model::rotate(float angle, const glm::vec3& axis) {
    // This is a simplified rotation - for proper rotation accumulation,
    // you'd want to use quaternions
    glm::mat4 rotationMatrix = glm::rotate(glm::mat4(1.0f), angle, axis);
    // Apply rotation to current rotation (simplified)
    markMatrixDirty();
}

void Model::scaleBy(const glm::vec3& factor) {
    scale *= factor;
    markMatrixDirty();
}

void Model::addMesh(std::shared_ptr<Mesh> mesh) {
    if (mesh) {
        meshes.push_back(mesh);
        boundingBoxCalculated = false;
        LOG_INFO("Added mesh to model (total: " + std::to_string(meshes.size()) + ")");
    }
}

void Model::removeMesh(size_t index) {
    if (index < meshes.size()) {
        meshes.erase(meshes.begin() + index);
        boundingBoxCalculated = false;
        LOG_INFO("Removed mesh from model (remaining: " + std::to_string(meshes.size()) + ")");
    }
}

std::shared_ptr<Mesh> Model::getMesh(size_t index) const {
    if (index < meshes.size()) {
        return meshes[index];
    }
    return nullptr;
}

void Model::clearMeshes() {
    meshes.clear();
    boundingBoxCalculated = false;
    LOG_INFO("Cleared all meshes from model");
}

bool Model::addTexture(const std::string& texturePath, size_t meshIndex) {
    if (meshIndex >= meshes.size()) {
        LOG_ERROR("Invalid mesh index for texture addition: " + std::to_string(meshIndex));
        return false;
    }
    
    auto texture = Texture::loadCached(texturePath);
    if (!texture) {
        LOG_ERROR("Failed to load texture: " + texturePath);
        return false;
    }
    
    meshes[meshIndex]->addTexture(texture, TextureType::DIFFUSE, "texture_diffuse");
    LOG_INFO("Added texture to mesh " + std::to_string(meshIndex) + ": " + texturePath);
    return true;
}

bool Model::addTexture(std::shared_ptr<Texture> texture, size_t meshIndex) {
    if (meshIndex >= meshes.size()) {
        LOG_ERROR("Invalid mesh index for texture addition: " + std::to_string(meshIndex));
        return false;
    }
    
    if (!texture || !texture->isValid()) {
        LOG_ERROR("Invalid texture provided");
        return false;
    }
    
    meshes[meshIndex]->addTexture(texture, TextureType::DIFFUSE, "texture_diffuse");
    LOG_INFO("Added texture to mesh " + std::to_string(meshIndex));
    return true;
}

void Model::removeTextures(size_t meshIndex) {
    if (meshIndex < meshes.size()) {
        meshes[meshIndex]->clearTextures();
        LOG_INFO("Removed textures from mesh " + std::to_string(meshIndex));
    }
}

void Model::setAnimationManager(std::shared_ptr<AnimationManager> animManager) {
    animationManager = animManager;
    LOG_INFO("Animation manager set for model");
}

bool Model::hasAnimations() const {
    return animationManager != nullptr;
}

void Model::updateAnimation(float deltaTime) {
    if (animationManager) {
        animationManager->updateAnimation(deltaTime);
    }
}

void Model::calculateBoundingBox() {
    if (meshes.empty()) {
        boundingBoxMin = glm::vec3(0.0f);
        boundingBoxMax = glm::vec3(0.0f);
        boundingBoxCalculated = true;
        return;
    }

    bool first = true;
    for (const auto& mesh : meshes) {
        if (!mesh) continue;

        for (const auto& vertex : mesh->vertices) {
            if (first) {
                boundingBoxMin = vertex.Position;
                boundingBoxMax = vertex.Position;
                first = false;
            } else {
                boundingBoxMin = glm::min(boundingBoxMin, vertex.Position);
                boundingBoxMax = glm::max(boundingBoxMax, vertex.Position);
            }
        }
    }

    // Calculate bounding radius
    glm::vec3 center = (boundingBoxMin + boundingBoxMax) * 0.5f;
    float maxDistance = 0.0f;

    for (const auto& mesh : meshes) {
        if (!mesh) continue;

        for (const auto& vertex : mesh->vertices) {
            float distance = glm::length(vertex.Position - center);
            maxDistance = std::max(maxDistance, distance);
        }
    }

    boundingRadius = maxDistance;
    boundingBoxCalculated = true;

    LOG_INFO("Calculated bounding box: min(" + std::to_string(boundingBoxMin.x) + ", " +
             std::to_string(boundingBoxMin.y) + ", " + std::to_string(boundingBoxMin.z) +
             ") max(" + std::to_string(boundingBoxMax.x) + ", " +
             std::to_string(boundingBoxMax.y) + ", " + std::to_string(boundingBoxMax.z) +
             ") radius: " + std::to_string(boundingRadius));
}

void Model::updateModelMatrix() const {
    modelMatrix = glm::mat4(1.0f);
    modelMatrix = glm::translate(modelMatrix, position);
    modelMatrix = glm::rotate(modelMatrix, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    modelMatrix = glm::rotate(modelMatrix, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    modelMatrix = glm::rotate(modelMatrix, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    modelMatrix = glm::scale(modelMatrix, scale);
    matrixNeedsUpdate = false;
}

void Model::markMatrixDirty() {
    matrixNeedsUpdate = true;
}

bool Model::loadTextures(const std::string& directory) {
    // This would load textures from a directory
    // Implementation depends on the specific requirements
    LOG_INFO("Loading textures from directory: " + directory);
    return true;
}

std::string Model::extractDirectory(const std::string& path) const {
    size_t lastSlash = path.find_last_of("/\\");
    if (lastSlash != std::string::npos) {
        return path.substr(0, lastSlash + 1);
    }
    return "";
}
