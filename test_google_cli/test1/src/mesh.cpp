#include <glad/glad.h>
#include "mesh.h"
#include "logger.h"
#include "error_handler.h"

Mesh::Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices) {
    this->vertices = vertices;
    this->indices = indices;

    setupMesh();
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Mesh created with " + std::to_string(vertices.size()) + " vertices and " + std::to_string(indices.size()) + " indices.", __FILE__, __LINE__);
}

void Mesh::Draw() {
    LOG_TRACE("Drawing mesh with VAO: " + std::to_string(VAO) + ", indices: " + std::to_string(indices.size()));

    if (VAO == 0) {
        LOG_ERROR("VAO is 0! Mesh not properly initialized.");
        return;
    }

    glBindVertexArray(VAO);
    CHECK_OPENGL("Bind VAO for drawing");

    glDrawElements(GL_TRIANGLES, indices.size(), GL_UNSIGNED_INT, 0);
    CHECK_OPENGL("Draw elements");

    glBindVertexArray(0);
    CHECK_OPENGL("Unbind VAO after drawing");

    LOG_TRACE("Mesh drawn successfully");
}

void Mesh::setupMesh() {
    LOG_TRACE("Setting up mesh with " + std::to_string(vertices.size()) + " vertices and " + std::to_string(indices.size()) + " indices");

    glGenVertexArrays(1, &VAO);
    glGenBuffers(1, &VBO);
    glGenBuffers(1, &EBO);

    LOG_TRACE("Generated VAO: " + std::to_string(VAO) + ", VBO: " + std::to_string(VBO) + ", EBO: " + std::to_string(EBO));
    CHECK_OPENGL("Generate buffers");

    glBindVertexArray(VAO);
    CHECK_OPENGL("Bind VAO");

    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    CHECK_OPENGL("Bind VBO");

    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(Vertex), &vertices[0], GL_STATIC_DRAW);
    CHECK_OPENGL("Upload vertex data");

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
    CHECK_OPENGL("Bind EBO");

    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(GLuint), &indices[0], GL_STATIC_DRAW);
    CHECK_OPENGL("Upload index data");

    // vertex positions
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)0);
    // vertex normals
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, Normal));
    // vertex texture coords
    glEnableVertexAttribArray(2);
    glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, TexCoords));
    // bone weights
    glEnableVertexAttribArray(3);
    glVertexAttribPointer(3, 4, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, BoneWeights));
    // bone indices
    glEnableVertexAttribArray(4);
    glVertexAttribIPointer(4, 4, GL_INT, sizeof(Vertex), (void*)offsetof(Vertex, BoneIndices));

    glBindVertexArray(0);
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Mesh VAO, VBO, EBO setup complete.", __FILE__, __LINE__);
}
