#include <glad/glad.h>
#include "mesh.h"
#include "texture.h"
#include "shader.h"
#include "logger.h"
#include "error_handler.h"

Mesh::Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices)
    : vertices(std::move(vertices)), indices(std::move(indices)), VAO(0), VBO(0), EBO(0), meshSetup(false) {
    setupMesh();
    LOG_INFO("Mesh created with " + std::to_string(this->vertices.size()) + " vertices and " + std::to_string(this->indices.size()) + " indices");
}

Mesh::Mesh(std::vector<Vertex> vertices, std::vector<GLuint> indices, std::vector<MeshTexture> textures)
    : vertices(std::move(vertices)), indices(std::move(indices)), textures(std::move(textures)),
      VAO(0), VBO(0), EBO(0), meshSetup(false) {
    setupMesh();
    LOG_INFO("Mesh created with " + std::to_string(this->vertices.size()) + " vertices, " +
             std::to_string(this->indices.size()) + " indices, and " + std::to_string(this->textures.size()) + " textures");
}

Mesh::~Mesh() {
    cleanup();
}

void Mesh::Draw(Shader* shader) {
    if (!isValid()) {
        LOG_ERROR("Cannot draw invalid mesh (VAO is 0)");
        return;
    }

    LOG_TRACE("Drawing mesh with VAO: " + std::to_string(VAO) + ", indices: " + std::to_string(indices.size()) + ", textures: " + std::to_string(textures.size()));

    // Bind textures
    bindTextures(shader);

    glBindVertexArray(VAO);
    CHECK_OPENGL("Bind VAO for drawing");

    glDrawElements(GL_TRIANGLES, static_cast<GLsizei>(indices.size()), GL_UNSIGNED_INT, 0);
    CHECK_OPENGL("Draw elements");

    glBindVertexArray(0);
    CHECK_OPENGL("Unbind VAO after drawing");

    LOG_TRACE("Mesh drawn successfully");
}

void Mesh::DrawInstanced(unsigned int instanceCount, Shader* shader) {
    if (!isValid()) {
        LOG_ERROR("Cannot draw invalid mesh (VAO is 0)");
        return;
    }

    LOG_TRACE("Drawing instanced mesh with " + std::to_string(instanceCount) + " instances");

    // Bind textures
    bindTextures(shader);

    glBindVertexArray(VAO);
    CHECK_OPENGL("Bind VAO for instanced drawing");

    glDrawElementsInstanced(GL_TRIANGLES, static_cast<GLsizei>(indices.size()), GL_UNSIGNED_INT, 0, instanceCount);
    CHECK_OPENGL("Draw elements instanced");

    glBindVertexArray(0);
    CHECK_OPENGL("Unbind VAO after instanced drawing");

    LOG_TRACE("Instanced mesh drawn successfully");
}

void Mesh::addTexture(std::shared_ptr<Texture> texture, TextureType type, const std::string& typeName) {
    if (!texture || !texture->isValid()) {
        LOG_WARNING("Attempting to add invalid texture to mesh");
        return;
    }

    // Remove existing texture of the same type
    removeTexture(type);

    std::string name = typeName.empty() ? "texture_" + std::to_string(static_cast<int>(type)) : typeName;
    textures.emplace_back(texture, type, name);

    LOG_INFO("Added texture '" + name + "' to mesh");
}

void Mesh::removeTexture(TextureType type) {
    auto it = std::remove_if(textures.begin(), textures.end(),
        [type](const MeshTexture& meshTex) { return meshTex.type == type; });

    if (it != textures.end()) {
        textures.erase(it, textures.end());
        LOG_INFO("Removed texture of type " + std::to_string(static_cast<int>(type)) + " from mesh");
    }
}

void Mesh::clearTextures() {
    textures.clear();
    LOG_INFO("Cleared all textures from mesh");
}

std::shared_ptr<Texture> Mesh::getTexture(TextureType type) const {
    for (const auto& meshTexture : textures) {
        if (meshTexture.type == type) {
            return meshTexture.texture;
        }
    }
    return nullptr;
}

bool Mesh::hasTexture(TextureType type) const {
    return getTexture(type) != nullptr;
}

void Mesh::updateMesh() {
    if (!meshSetup) {
        setupMesh();
        return;
    }

    // Update existing buffers
    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(Vertex), vertices.data(), GL_STATIC_DRAW);
    CHECK_OPENGL("Update vertex data");

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(GLuint), indices.data(), GL_STATIC_DRAW);
    CHECK_OPENGL("Update index data");

    glBindBuffer(GL_ARRAY_BUFFER, 0);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);

    LOG_INFO("Mesh data updated");
}

void Mesh::setupMesh() {
    if (meshSetup) {
        LOG_WARNING("Mesh already set up, skipping");
        return;
    }

    LOG_TRACE("Setting up mesh with " + std::to_string(vertices.size()) + " vertices and " + std::to_string(indices.size()) + " indices");

    glGenVertexArrays(1, &VAO);
    glGenBuffers(1, &VBO);
    glGenBuffers(1, &EBO);

    LOG_TRACE("Generated VAO: " + std::to_string(VAO) + ", VBO: " + std::to_string(VBO) + ", EBO: " + std::to_string(EBO));
    CHECK_OPENGL("Generate buffers");

    glBindVertexArray(VAO);
    CHECK_OPENGL("Bind VAO");

    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    CHECK_OPENGL("Bind VBO");

    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(Vertex), vertices.data(), GL_STATIC_DRAW);
    CHECK_OPENGL("Upload vertex data");

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
    CHECK_OPENGL("Bind EBO");

    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(GLuint), indices.data(), GL_STATIC_DRAW);
    CHECK_OPENGL("Upload index data");

    // vertex positions
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)0);
    // vertex normals
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, Normal));
    // vertex texture coords
    glEnableVertexAttribArray(2);
    glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, TexCoords));
    // bone weights
    glEnableVertexAttribArray(3);
    glVertexAttribPointer(3, 4, GL_FLOAT, GL_FALSE, sizeof(Vertex), (void*)offsetof(Vertex, BoneWeights));
    // bone indices
    glEnableVertexAttribArray(4);
    glVertexAttribIPointer(4, 4, GL_INT, sizeof(Vertex), (void*)offsetof(Vertex, BoneIndices));

    glBindVertexArray(0);
    CHECK_OPENGL("Setup mesh complete");

    meshSetup = true;
    LOG_INFO("Mesh VAO, VBO, EBO setup complete");
}

void Mesh::bindTextures(Shader* shader) {
    unsigned int diffuseNr = 1;
    unsigned int specularNr = 1;
    unsigned int normalNr = 1;
    unsigned int heightNr = 1;

    for (unsigned int i = 0; i < textures.size(); i++) {
        const auto& meshTexture = textures[i];
        if (!meshTexture.texture || !meshTexture.texture->isValid()) {
            continue;
        }

        std::string number;
        std::string name = meshTexture.typeName;

        if (name == "texture_diffuse") {
            number = std::to_string(diffuseNr++);
        } else if (name == "texture_specular") {
            number = std::to_string(specularNr++);
        } else if (name == "texture_normal") {
            number = std::to_string(normalNr++);
        } else if (name == "texture_height") {
            number = std::to_string(heightNr++);
        }

        // Bind texture
        meshTexture.texture->bind(i);

        // Set shader uniform if shader is provided
        if (shader) {
            shader->setInt((name + number).c_str(), i);
        }
    }
}

void Mesh::cleanup() {
    if (VAO != 0) {
        glDeleteVertexArrays(1, &VAO);
        VAO = 0;
    }
    if (VBO != 0) {
        glDeleteBuffers(1, &VBO);
        VBO = 0;
    }
    if (EBO != 0) {
        glDeleteBuffers(1, &EBO);
        EBO = 0;
    }
    meshSetup = false;
    LOG_TRACE("Mesh OpenGL resources cleaned up");
}
