#include "application.h"
#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include "camera.h"
#include "model_controller.h"
#include "animation.h"
#include "model.h"
#include "shader.h"
#include "gltf.h"
#include "logger.h"
#include "error_handler.h"
#include <iostream>

// Static instance for callbacks
Application* Application::instance = nullptr;

Application::Application(int width, int height, const std::string& title)
    : window(nullptr), screenWidth(width), screenHeight(height), windowTitle(title),
      initialized(false), deltaTime(0.0f), lastFrame(0.0f), rightMousePressed(false),
      lastX(width / 2.0f), lastY(height / 2.0f), firstMouse(true) {
    
    instance = this;
    LOG_INFO("Application created: " + title + " (" + std::to_string(width) + "x" + std::to_string(height) + ")");
}

Application::~Application() {
    shutdown();
    instance = nullptr;
    LOG_INFO("Application destroyed");
}

bool Application::initialize() {
    if (initialized) {
        LOG_WARNING("Application already initialized");
        return true;
    }

    LOG_INFO("Initializing application...");

    if (!initializeGLFW()) {
        LOG_ERROR("Failed to initialize GLFW");
        return false;
    }

    if (!initializeOpenGL()) {
        LOG_ERROR("Failed to initialize OpenGL");
        return false;
    }

    // Initialize core components
    camera = std::make_unique<Camera>(glm::vec3(0.0f, 0.0f, 3.0f));
    modelController = std::make_unique<ModelController>(camera.get(), screenWidth, screenHeight);

    if (!loadShaders()) {
        LOG_ERROR("Failed to load shaders");
        return false;
    }

    if (!loadModel()) {
        LOG_ERROR("Failed to load model");
        return false;
    }

    setupCallbacks();
    
    // Setup some example obstacles
    modelController->addBoxObstacle(glm::vec3(3.0f, 0.0f, 0.0f), glm::vec3(0.5f, 1.0f, 0.5f));
    modelController->addSphereObstacle(glm::vec3(-3.0f, 0.0f, 0.0f), 1.0f);
    
    // Add the main model to the controller
    modelController->addModel(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(2.0f, 2.0f, 2.0f), 1.5f);

    printControls();
    
    initialized = true;
    LOG_INFO("Application initialized successfully");
    return true;
}

void Application::run() {
    if (!initialized) {
        LOG_ERROR("Application not initialized");
        return;
    }

    LOG_INFO("Starting main application loop");

    while (!shouldClose()) {
        calculateDeltaTime();
        
        processInput();
        update();
        render();
        
        glfwSwapBuffers(window);
        glfwPollEvents();
    }

    LOG_INFO("Application loop ended");
}

void Application::shutdown() {
    if (!initialized) {
        return;
    }

    LOG_INFO("Shutting down application...");

    // Clean up components
    mainModel.reset();
    animationManager.reset();
    modelController.reset();
    camera.reset();
    shader.reset();

    // Clean up GLFW
    if (window) {
        glfwDestroyWindow(window);
        window = nullptr;
    }
    glfwTerminate();

    initialized = false;
    LOG_INFO("Application shutdown complete");
}

void Application::setWindowSize(int width, int height) {
    screenWidth = width;
    screenHeight = height;
    
    if (modelController) {
        modelController->setScreenSize(width, height);
    }
    
    glViewport(0, 0, width, height);
    LOG_INFO("Window size updated: " + std::to_string(width) + "x" + std::to_string(height));
}

bool Application::shouldClose() const {
    return window ? glfwWindowShouldClose(window) : true;
}

bool Application::initializeGLFW() {
    glfwInit();
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

#ifdef __APPLE__
    glfwWindowHint(GLFW_OPENGL_FORWARD_COMPAT, GL_TRUE);
#endif

    window = glfwCreateWindow(screenWidth, screenHeight, windowTitle.c_str(), NULL, NULL);
    if (!window) {
        LOG_ERROR("Failed to create GLFW window");
        glfwTerminate();
        return false;
    }

    glfwMakeContextCurrent(window);
    glfwSetInputMode(window, GLFW_CURSOR, GLFW_CURSOR_NORMAL);

    LOG_INFO("GLFW initialized successfully");
    return true;
}

bool Application::initializeOpenGL() {
    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        LOG_ERROR("Failed to initialize GLAD");
        return false;
    }

    glEnable(GL_DEPTH_TEST);
    glViewport(0, 0, screenWidth, screenHeight);

    // Print OpenGL info
    LOG_INFO("OpenGL Version: " + std::string((char*)glGetString(GL_VERSION)));
    LOG_INFO("OpenGL Renderer: " + std::string((char*)glGetString(GL_RENDERER)));
    LOG_INFO("OpenGL Vendor: " + std::string((char*)glGetString(GL_VENDOR)));

    return true;
}

bool Application::loadShaders() {
    try {
        shader = std::make_unique<Shader>("shaders/vertex_shader.glsl", "shaders/fragment_shader.glsl");
        LOG_INFO("Shaders loaded successfully");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to load shaders: " + std::string(e.what()));
        return false;
    }
}

bool Application::loadModel() {
    try {
        // Load glTF model
        Gltf::GltfModel gltfModel;
        if (!Gltf::loadGLTF("rectangle.gltf", gltfModel)) {
            LOG_ERROR("Failed to load glTF model");
            return false;
        }

        // Create Model from glTF data
        mainModel = std::make_unique<Model>();
        if (!mainModel->loadFromGLTF(gltfModel)) {
            LOG_ERROR("Failed to create Model from glTF data");
            return false;
        }

        // Initialize animation manager
        animationManager = std::make_unique<AnimationManager>(&gltfModel);
        mainModel->setAnimationManager(std::shared_ptr<AnimationManager>(animationManager.get(), [](AnimationManager*){}));

        // Start first animation if available
        if (!gltfModel.animations.empty()) {
            animationManager->playAnimation(0, true);
            LOG_INFO("Started playing animation 0");
        }

        LOG_INFO("Model loaded successfully");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to load model: " + std::string(e.what()));
        return false;
    }
}

void Application::setupCallbacks() {
    glfwSetFramebufferSizeCallback(window, framebufferSizeCallback);
    glfwSetCursorPosCallback(window, mouseCallback);
    glfwSetMouseButtonCallback(window, mouseButtonCallback);
    glfwSetScrollCallback(window, scrollCallback);
    glfwSetKeyCallback(window, keyCallback);
    
    LOG_INFO("Callbacks set up");
}

void Application::processInput() {
    if (glfwGetKey(window, GLFW_KEY_ESCAPE) == GLFW_PRESS) {
        glfwSetWindowShouldClose(window, true);
    }

    // Camera movement (WASD)
    if (glfwGetKey(window, GLFW_KEY_W) == GLFW_PRESS)
        camera->ProcessKeyboard(FORWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_S) == GLFW_PRESS)
        camera->ProcessKeyboard(BACKWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_A) == GLFW_PRESS)
        camera->ProcessKeyboard(LEFT, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_D) == GLFW_PRESS)
        camera->ProcessKeyboard(RIGHT, deltaTime);

    // Model controller input
    if (modelController) {
        modelController->handleKeyboard(window, deltaTime);
    }
}

void Application::update() {
    if (modelController) {
        modelController->update(deltaTime);
    }

    if (animationManager) {
        animationManager->updateAnimation(deltaTime);
    }

    if (mainModel) {
        mainModel->updateAnimation(deltaTime);
    }
}

void Application::render() {
    glClearColor(0.1f, 0.1f, 0.1f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    if (!shader || !mainModel) {
        return;
    }

    shader->use();

    // Set view and projection matrices
    glm::mat4 view = camera->GetViewMatrix();
    glm::mat4 projection = glm::perspective(glm::radians(camera->Zoom), 
                                          (float)screenWidth / (float)screenHeight, 
                                          0.1f, 100.0f);
    
    shader->setMat4("view", glm::value_ptr(view));
    shader->setMat4("projection", glm::value_ptr(projection));

    // Set lighting uniforms
    glm::vec3 lightPos(1.2f, 1.0f, 2.0f);
    glm::vec3 lightColor(1.0f, 1.0f, 1.0f);
    
    // Change color based on selection state
    glm::vec3 objectColor(1.0f, 0.5f, 0.31f); // Default orange
    if (modelController && modelController->getSelectedModelIndex() >= 0) {
        objectColor = glm::vec3(0.2f, 1.0f, 0.2f); // Green when selected
    }

    shader->setVec3("lightPos", lightPos);
    shader->setVec3("lightColor", lightColor);
    shader->setVec3("objectColor", objectColor);
    shader->setVec3("viewPos", camera->Position);
    shader->setBool("hasTexture", true);

    // Set model matrix from controller
    if (modelController && modelController->getModelCount() > 0) {
        glm::mat4 modelMatrix = modelController->getModelMatrix(0);
        shader->setMat4("model", glm::value_ptr(modelMatrix));
    }

    // Draw the main model
    mainModel->draw(shader.get());

    // Draw obstacles for visual feedback
    if (modelController) {
        const auto& obstacles = modelController->getObstacles();
        for (const auto& obstacle : obstacles) {
            glm::mat4 obstacleModel = glm::mat4(1.0f);
            obstacleModel = glm::translate(obstacleModel, obstacle.position);
            
            if (obstacle.radius > 0.0f) {
                obstacleModel = glm::scale(obstacleModel, glm::vec3(obstacle.radius));
            } else {
                obstacleModel = glm::scale(obstacleModel, obstacle.size * 2.0f);
            }
            
            shader->setMat4("model", glm::value_ptr(obstacleModel));
            shader->setVec3("objectColor", glm::vec3(1.0f, 0.0f, 0.0f)); // Red for obstacles
            shader->setBool("hasTexture", false);
            
            // Draw wireframe
            glPolygonMode(GL_FRONT_AND_BACK, GL_LINE);
            mainModel->draw(shader.get());
            glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
        }
    }
}

void Application::calculateDeltaTime() {
    float currentFrame = static_cast<float>(glfwGetTime());
    deltaTime = currentFrame - lastFrame;
    lastFrame = currentFrame;
}

void Application::printControls() {
    LOG_INFO("=== CONTROLS ===");
    LOG_INFO("Left Click: Select and drag the model");
    LOG_INFO("Right Click + Mouse: Look around");
    LOG_INFO("Arrow Keys: Move selected model");
    LOG_INFO("WASD: Move camera");
    LOG_INFO("ESC: Exit");
    LOG_INFO("Model turns GREEN when selected, RED obstacles block movement");
    LOG_INFO("Window boundaries are enabled by default");
}

// Static callback implementations
void Application::framebufferSizeCallback(GLFWwindow* window, int width, int height) {
    (void)window;
    if (instance) {
        instance->onFramebufferSizeChanged(width, height);
    }
}

void Application::mouseCallback(GLFWwindow* window, double xpos, double ypos) {
    if (instance) {
        instance->onMouseMoved(xpos, ypos);
    }
}

void Application::mouseButtonCallback(GLFWwindow* window, int button, int action, int mods) {
    if (instance) {
        instance->onMouseButtonPressed(button, action, mods);
    }
}

void Application::scrollCallback(GLFWwindow* window, double xoffset, double yoffset) {
    (void)window;
    if (instance) {
        instance->onScrolled(xoffset, yoffset);
    }
}

void Application::keyCallback(GLFWwindow* window, int key, int scancode, int action, int mods) {
    (void)window; (void)scancode; (void)mods;
    if (instance) {
        instance->onKeyPressed(key, scancode, action, mods);
    }
}

// Callback implementations
void Application::onFramebufferSizeChanged(int width, int height) {
    setWindowSize(width, height);
}

void Application::onMouseMoved(double xpos, double ypos) {
    if (firstMouse) {
        lastX = static_cast<float>(xpos);
        lastY = static_cast<float>(ypos);
        firstMouse = false;
    }

    // Handle model controller mouse movement
    if (modelController) {
        modelController->handleMouseMove(window, xpos, ypos);
    }

    // Only process camera movement if right mouse button is pressed and not dragging a model
    if (rightMousePressed && (!modelController || !modelController->isDragging())) {
        float xoffset = static_cast<float>(xpos) - lastX;
        float yoffset = lastY - static_cast<float>(ypos); // Reversed since y-coordinates go from bottom to top

        camera->ProcessMouseMovement(xoffset, yoffset);
    }

    lastX = static_cast<float>(xpos);
    lastY = static_cast<float>(ypos);
}

void Application::onMouseButtonPressed(int button, int action, int mods) {
    if (button == GLFW_MOUSE_BUTTON_RIGHT) {
        if (action == GLFW_PRESS) {
            rightMousePressed = true;
        } else if (action == GLFW_RELEASE) {
            rightMousePressed = false;
        }
    }

    // Handle model controller mouse button events
    if (modelController) {
        modelController->handleMouseButton(window, button, action, mods);
    }
}

void Application::onScrolled(double xoffset, double yoffset) {
    (void)xoffset;
    camera->ProcessMouseScroll(static_cast<float>(yoffset));
}

void Application::onKeyPressed(int key, int scancode, int action, int mods) {
    (void)scancode; (void)mods;

    if (key == GLFW_KEY_B && action == GLFW_PRESS) {
        // Toggle window boundaries
        if (modelController) {
            bool enabled = !modelController->getWindowBoundariesEnabled();
            modelController->setWindowBoundariesEnabled(enabled);
            LOG_INFO("Window boundaries " + std::string(enabled ? "enabled" : "disabled"));
        }
    }
}
