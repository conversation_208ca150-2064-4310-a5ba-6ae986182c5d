#include "application.h"
#include "logger.h"
#include "error_handler.h"

#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"

#include <iostream>

int SCR_WIDTH = 800, SCR_HEIGHT = 600;

int main() {
    // Initialize logging
    Utils::Logger::getInstance().setMinLevel(Utils::Logger::TRACE   );
    LOG_INFO("Starting 3D Engine Application");

    try {
        // Create and initialize application
        Application app(SCR_WIDTH, SCR_HEIGHT, "Refactored 3D Engine");

        if (!app.initialize()) {
            LOG_ERROR("Failed to initialize application");
            return -1;
        }

        // Run the application
        app.run();

        // Shutdown is handled automatically by destructor
        LOG_INFO("Application completed successfully");

    } catch (const std::exception& e) {
        LOG_ERROR("Application error: " + std::string(e.what()));
        return -1;
    }

    // Print final statistics
    Utils::Logger::getInstance().printStatistics();
    Utils::ErrorHandler::getInstance().printErrorSummary();

    return 0;
}