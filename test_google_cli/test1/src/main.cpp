#include <glad/glad.h>
#include <GLFW/glfw3.h>

#include <iostream>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

#include "shader.h"
#include "camera.h"
#include "mesh.h"
#include "gltf.h"
#include "animation.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <chrono>

#include "logger.h"
#include "error_handler.h"

// STB Image implementation - must be included before any other stb_image includes
#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"

// Enhanced OpenGL error checking function
void checkGLError(const std::string& operation) {
    Utils::ErrorHandler::getInstance().checkOpenGLError(operation, __FILE__, __LINE__);
}

// Screenshot function to capture framebuffer
void takeScreenshot(int width, int height, const std::string& filename) {
    // Create failure directory if it doesn't exist
    std::filesystem::create_directories("failure");

    // Allocate memory for pixel data
    std::vector<unsigned char> pixels(width * height * 3);

    // Read pixels from framebuffer
    glReadPixels(0, 0, width, height, GL_RGB, GL_UNSIGNED_BYTE, pixels.data());

    // Create PPM file (simple image format)
    std::string filepath = "failure/" + filename;
    std::ofstream file(filepath, std::ios::binary);
    if (file.is_open()) {
        // PPM header
        file << "P6\n" << width << " " << height << "\n255\n";

        // Write pixel data (flip vertically since OpenGL origin is bottom-left)
        for (int y = height - 1; y >= 0; y--) {
            for (int x = 0; x < width; x++) {
                int index = (y * width + x) * 3;
                file.write(reinterpret_cast<const char*>(&pixels[index]), 3);
            }
        }
        file.close();
        LOG_INFO("Screenshot saved: " + filepath);
    } else {
        LOG_ERROR("Failed to save screenshot: " + filepath);
    }
}

// Function to read file content into a string
std::string readFileIntoString(const std::string& path) {
    std::ifstream input_file(path);
    if (!input_file.is_open()) {
        Utils::ErrorHandler::getInstance().handleError("Could not open file " + path, false, __FILE__, __LINE__);
        return "";
    }

    // Check if file is empty
    input_file.seekg(0, std::ios::end);
    size_t fileSize = input_file.tellg();
    input_file.seekg(0, std::ios::beg);

    if (fileSize == 0) {
        Utils::ErrorHandler::getInstance().handleError("File is empty: " + path, false, __FILE__, __LINE__);
        return "";
    }

    std::stringstream sstr;
    sstr << input_file.rdbuf();
    std::string content = sstr.str();

    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Successfully read file: " + path + " (" + std::to_string(fileSize) + " bytes)");
    return content;
}

// Callbacks
void framebuffer_size_callback(GLFWwindow* window, int width, int height);
void mouse_callback(GLFWwindow* window, double xpos, double ypos);
void scroll_callback(GLFWwindow* window, double xoffset, double yoffset);
void processInput(GLFWwindow *window);

// Texture loading
GLuint loadTexture(const std::string& imagePath);

// Settings
const unsigned int SCR_WIDTH = 800;
const unsigned int SCR_HEIGHT = 600;

// Camera
Camera camera(glm::vec3(0.0f, 0.0f, 3.0f));
float lastX = SCR_WIDTH / 2.0f;
float lastY = SCR_HEIGHT / 2.0f;
bool firstMouse = true;

bool makeRotation = false;

// Timing
float deltaTime = 0.0f;
float lastFrame = 0.0f;

// Placeholder for error callback
void error_callback(int error, const char* description) {
    Utils::ErrorHandler::getInstance().handleError(std::string("GLFW Error (") + std::to_string(error) + "): " + description, false, __FILE__, __LINE__);
}

int main() {
    // Initialize enhanced logging
    Utils::Logger::getInstance().setLogFile("engine.log");
    Utils::Logger::getInstance().setTraceFile("engine_trace.log");
    Utils::Logger::getInstance().enableColors(true);
    Utils::Logger::getInstance().setMinLevel(Utils::Logger::TRACE);

    LOG_INFO("=== Starting Simple 3D Engine with Enhanced Logging ===");
    LOG_INFO("Logging system initialized with colors and detailed tracing");

    if (!glfwInit()) {
        HANDLE_ERROR("Failed to initialize GLFW", true);
        return -1;
    }

    glfwSetErrorCallback(error_callback);

    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    GLFWwindow* window = glfwCreateWindow(SCR_WIDTH, SCR_HEIGHT, "Simple 3D Engine with Animations", NULL, NULL);
    if (!window) {
        HANDLE_ERROR("Failed to create GLFW window", true);
        glfwTerminate();
        return -1;
    }

    LOG_INFO("GLFW window created successfully (" + std::to_string(SCR_WIDTH) + "x" + std::to_string(SCR_HEIGHT) + ")");

    glfwMakeContextCurrent(window);
    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        HANDLE_ERROR("Failed to initialize GLAD", true);
        return -1;
    }

    // Log detailed OpenGL information
    LOG_INFO("OpenGL Version: " + std::string((char*)glGetString(GL_VERSION)));
    LOG_INFO("GLSL Version: " + std::string((char*)glGetString(GL_SHADING_LANGUAGE_VERSION)));
    LOG_INFO("OpenGL Vendor: " + std::string((char*)glGetString(GL_VENDOR)));
    LOG_INFO("OpenGL Renderer: " + std::string((char*)glGetString(GL_RENDERER)));

    GLint maxVertexAttribs;
    glGetIntegerv(GL_MAX_VERTEX_ATTRIBS, &maxVertexAttribs);
    LOG_INFO("Max vertex attributes: " + std::to_string(maxVertexAttribs));

    CHECK_OPENGL("GLAD initialization");
    glfwSetFramebufferSizeCallback(window, framebuffer_size_callback);
    glfwSetCursorPosCallback(window, mouse_callback);
    glfwSetScrollCallback(window, scroll_callback);

    // tell GLFW to capture our mouse
    glfwSetInputMode(window, GLFW_CURSOR, GLFW_CURSOR_DISABLED);

    // build and compile our shader program
    LOG_INFO("Loading shaders...");
    Shader ourShader("../src/simple.vert", "../src/simple.frag");
    LOG_INFO("Shader program compiled successfully with ID: " + std::to_string(ourShader.ID));
    CHECK_OPENGL("Shader compilation");

    // Load and parse animated glTF file
    LOG_INFO("Loading animated glTF file: ../rectangle.gltf");
    std::string gltfContent = readFileIntoString("../rectangle.gltf");
    if (gltfContent.empty()) {
        HANDLE_ERROR("Failed to read glTF file: animated_character.gltf", true);
        return -1;
    }

    Gltf::GltfModel gltfModel;
    try {
        gltfModel = Gltf::GltfParser::parse(gltfContent, "./");
        LOG_INFO("glTF file parsed successfully");
        LOG_INFO("  Nodes: " + std::to_string(gltfModel.nodes.size()));
        LOG_INFO("  Meshes: " + std::to_string(gltfModel.meshes.size()));
        LOG_INFO("  Materials: " + std::to_string(gltfModel.materials.size()));
        LOG_INFO("  Animations: " + std::to_string(gltfModel.animations.size()));
        LOG_INFO("  Skins: " + std::to_string(gltfModel.skins.size()));
        LOG_INFO("  Buffers: " + std::to_string(gltfModel.buffers.size()));
    } catch (const std::runtime_error& e) {
        HANDLE_ERROR("Error parsing glTF: " + std::string(e.what()), true);
        return -1;
    }

    // Validate and select mesh
    if (gltfModel.meshes.empty()) {
        HANDLE_ERROR("No meshes found in glTF model", true);
        return -1;
    }

    if (gltfModel.meshes[0].primitives.empty()) {
        HANDLE_ERROR("No primitives found in first mesh", true);
        return -1;
    }

    const Gltf::Primitive& primitive = gltfModel.meshes[0].primitives[0];
    LOG_INFO("Using mesh 0, primitive 0");
    LOG_INFO("Primitive attributes:");
    for (const auto& attr : primitive.attributes) {
        LOG_INFO("  " + attr.first + ": accessor " + std::to_string(attr.second));
    }
    LOG_INFO("Primitive indices: " + (primitive.indices != -1 ? std::to_string(primitive.indices) : "none"));

    std::vector<Vertex> meshVertices;
    std::vector<GLuint> meshIndices;

    // Get position data
    if (primitive.attributes.count("POSITION")) {
        const Gltf::Accessor& posAccessor = gltfModel.accessors[primitive.attributes.at("POSITION")];
        const Gltf::BufferView& posBufferView = gltfModel.bufferViews[posAccessor.bufferView];
        const Gltf::Buffer& posBuffer = gltfModel.buffers[posBufferView.buffer];

        size_t vertexCount = posAccessor.count;
        LOG_INFO("Loading " + std::to_string(vertexCount) + " vertices");

        // Validate accessor
        if (posAccessor.type != Gltf::Type::VEC3) {
            HANDLE_ERROR("POSITION accessor is not VEC3 type", true);
            return -1;
        }

        meshVertices.resize(vertexCount);
        const float* positions = reinterpret_cast<const float*>(posBuffer.data.data() + posBufferView.byteOffset + posAccessor.byteOffset);

        // Initialize all vertices with defaults
        for (size_t i = 0; i < vertexCount; ++i) {
            meshVertices[i].Position = glm::vec3(positions[i * 3], positions[i * 3 + 1], positions[i * 3 + 2]);
            meshVertices[i].Normal = glm::vec3(0.0f, 0.0f, 1.0f); // Default normal
            meshVertices[i].TexCoords = glm::vec2(0.0f, 0.0f); // Default texture coordinates
            meshVertices[i].BoneWeights = glm::vec4(0.0f); // Default no bone influence
            meshVertices[i].BoneIndices = glm::ivec4(0); // Default bone indices
        }

        // Log first few vertices for debugging
        LOG_TRACE("First 3 vertex positions:");
        for (size_t i = 0; i < std::min(vertexCount, size_t(3)); ++i) {
            LOG_VECTOR3("Vertex " + std::to_string(i), meshVertices[i].Position.x, meshVertices[i].Position.y, meshVertices[i].Position.z);
        }

        LOG_INFO("Position data loaded successfully");
    } else {
        HANDLE_ERROR("POSITION attribute not found in glTF primitive", true);
        return -1;
    }

    // Get normal data
    if (primitive.attributes.count("NORMAL")) {
        const Gltf::Accessor& normalAccessor = gltfModel.accessors[primitive.attributes.at("NORMAL")];
        const Gltf::BufferView& normalBufferView = gltfModel.bufferViews[normalAccessor.bufferView];
        const Gltf::Buffer& normalBuffer = gltfModel.buffers[normalBufferView.buffer];

        const float* normals = reinterpret_cast<const float*>(normalBuffer.data.data() + normalBufferView.byteOffset + normalAccessor.byteOffset);

        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshVertices[i].Normal = glm::vec3(normals[i * 3], normals[i * 3 + 1], normals[i * 3 + 2]);
        }
    } else {
        Utils::Logger::getInstance().log(Utils::Logger::WARNING, "NORMAL attribute not found in glTF primitive. Normals will be default (0,0,0).", __FILE__, __LINE__);
    }

    // Get texture coordinates (TEXCOORD_0)
    if (primitive.attributes.count("TEXCOORD_0")) {
        const Gltf::Accessor& texCoordAccessor = gltfModel.accessors[primitive.attributes.at("TEXCOORD_0")];
        const Gltf::BufferView& texCoordBufferView = gltfModel.bufferViews[texCoordAccessor.bufferView];
        const Gltf::Buffer& texCoordBuffer = gltfModel.buffers[texCoordBufferView.buffer];

        const float* texCoords = reinterpret_cast<const float*>(texCoordBuffer.data.data() + texCoordBufferView.byteOffset + texCoordAccessor.byteOffset);

        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshVertices[i].TexCoords = glm::vec2(texCoords[i * 2], texCoords[i * 2 + 1]);
        }
        Utils::Logger::getInstance().log(Utils::Logger::INFO, "Loaded texture coordinates from glTF.", __FILE__, __LINE__);
    } else {
        Utils::Logger::getInstance().log(Utils::Logger::WARNING, "No texture coordinates found in glTF.", __FILE__, __LINE__);
    }

    // Get bone weights (WEIGHTS_0)
    if (primitive.attributes.count("WEIGHTS_0")) {
        const Gltf::Accessor& weightsAccessor = gltfModel.accessors[primitive.attributes.at("WEIGHTS_0")];
        const Gltf::BufferView& weightsBufferView = gltfModel.bufferViews[weightsAccessor.bufferView];
        const Gltf::Buffer& weightsBuffer = gltfModel.buffers[weightsBufferView.buffer];

        const float* weights = reinterpret_cast<const float*>(weightsBuffer.data.data() + weightsBufferView.byteOffset + weightsAccessor.byteOffset);

        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshVertices[i].BoneWeights = glm::vec4(weights[i * 4], weights[i * 4 + 1], weights[i * 4 + 2], weights[i * 4 + 3]);
        }
        Utils::Logger::getInstance().log(Utils::Logger::INFO, "Loaded bone weights from glTF.", __FILE__, __LINE__);
    }

    // Get bone indices (JOINTS_0)
    if (primitive.attributes.count("JOINTS_0")) {
        const Gltf::Accessor& jointsAccessor = gltfModel.accessors[primitive.attributes.at("JOINTS_0")];
        const Gltf::BufferView& jointsBufferView = gltfModel.bufferViews[jointsAccessor.bufferView];
        const Gltf::Buffer& jointsBuffer = gltfModel.buffers[jointsBufferView.buffer];

        if (jointsAccessor.componentType == Gltf::ComponentType::UNSIGNED_BYTE) {
            const unsigned char* joints = reinterpret_cast<const unsigned char*>(jointsBuffer.data.data() + jointsBufferView.byteOffset + jointsAccessor.byteOffset);
            for (size_t i = 0; i < meshVertices.size(); ++i) {
                meshVertices[i].BoneIndices = glm::ivec4(joints[i * 4], joints[i * 4 + 1], joints[i * 4 + 2], joints[i * 4 + 3]);
            }
        } else if (jointsAccessor.componentType == Gltf::ComponentType::UNSIGNED_SHORT) {
            const unsigned short* joints = reinterpret_cast<const unsigned short*>(jointsBuffer.data.data() + jointsBufferView.byteOffset + jointsAccessor.byteOffset);
            for (size_t i = 0; i < meshVertices.size(); ++i) {
                meshVertices[i].BoneIndices = glm::ivec4(joints[i * 4], joints[i * 4 + 1], joints[i * 4 + 2], joints[i * 4 + 3]);
            }
        }
        Utils::Logger::getInstance().log(Utils::Logger::INFO, "Loaded bone indices from glTF.", __FILE__, __LINE__);
    }

    // Get index data
    if (primitive.indices != -1) {
        const Gltf::Accessor& indexAccessor = gltfModel.accessors[primitive.indices];
        const Gltf::BufferView& indexBufferView = gltfModel.bufferViews[indexAccessor.bufferView];
        const Gltf::Buffer& indexBuffer = gltfModel.buffers[indexBufferView.buffer];

        size_t indexCount = indexAccessor.count;
        meshIndices.resize(indexCount);

        // Handle different component types for indices
        if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_SHORT) {
            const unsigned short* indices = reinterpret_cast<const unsigned short*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_INT) {
            const unsigned int* indices = reinterpret_cast<const unsigned int*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_BYTE) {
            const unsigned char* indices = reinterpret_cast<const unsigned char*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else {
            Utils::ErrorHandler::getInstance().handleError("Unsupported index component type.", true, __FILE__, __LINE__);
            return -1;
        }
    } else {
        // If no indices, assume non-indexed drawing (e.g., triangles list)
        // For simplicity, we'll just use a sequential index buffer if none is provided
        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshIndices.push_back(static_cast<GLuint>(i));
        }
    }

    // Load texture if available
    GLuint textureID = 0;
    if (!gltfModel.materials.empty() && !gltfModel.textures.empty() && !gltfModel.images.empty()) {
        const auto& material = gltfModel.materials[0];
        if (material.pbrMetallicRoughness.baseColorTexture.index >= 0) {
            const auto& texture = gltfModel.textures[material.pbrMetallicRoughness.baseColorTexture.index];
            const auto& image = gltfModel.images[texture.source];

            LOG_INFO("Attempting to load texture: " + image.uri);
            textureID = loadTexture(image.uri);

            if (textureID == 0) {
                LOG_WARNING("Failed to load texture: " + image.uri + ", using default material");
            } else {
                LOG_INFO("Successfully loaded texture ID: " + std::to_string(textureID));
            }
        }
    }

    // Create mesh with detailed logging from glTF data
    LOG_INFO("Creating mesh with " + std::to_string(meshVertices.size()) + " vertices and " + std::to_string(meshIndices.size()) + " indices");
    Mesh gltfMesh(meshVertices, meshIndices, textureID);
    LOG_INFO("Animated mesh created successfully with texture ID: " + std::to_string(textureID));
    CHECK_OPENGL("Mesh creation");

    // Initialize animation manager
    LOG_INFO("Initializing animation manager...");
    AnimationManager animationManager(&gltfModel);

    // Start first animation if available
    if (!gltfModel.animations.empty()) {
        animationManager.playAnimation(0, true);
        LOG_INFO("Started playing animation 0");
    } else {
        LOG_INFO("No animations found in glTF model");
    }

    // Setup OpenGL state
    LOG_INFO("Setting up OpenGL state...");
    glEnable(GL_DEPTH_TEST);
    glDepthFunc(GL_LESS);

    // Disable face culling to ensure triangle is visible from both sides
    glDisable(GL_CULL_FACE);

    // Enable filled polygon mode (disable wireframe)
    glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
    CHECK_OPENGL("Enable filled polygon mode");

    CHECK_OPENGL("OpenGL state setup");

    // Log initial camera position
    LOG_VECTOR3("Initial camera position", camera.Position.x, camera.Position.y, camera.Position.z);
    LOG_VECTOR3("Initial camera front", camera.Front.x, camera.Front.y, camera.Front.z);

    // Main loop
    LOG_INFO("Starting main rendering loop...");
    int frameCount = 0;
    float lastLogTime = 0.0f;

    // Screenshot timing
    float lastScreenshotTime = 0.0f;
    const float screenshotInterval = 3.0f; // Take screenshot every 3 seconds
    int screenshotCount = 0;

    while (!glfwWindowShouldClose(window)) {
        // per-frame time logic
        float currentFrame = glfwGetTime();
        deltaTime = currentFrame - lastFrame;
        lastFrame = currentFrame;
        frameCount++;

        // Log detailed info every 5 seconds
        if (currentFrame - lastLogTime > 5.0f) {
            float fps = frameCount / (currentFrame - lastLogTime);
            LOG_INFO("FPS: " + std::to_string(fps) + " | Frame: " + std::to_string(frameCount));
            LOG_VECTOR3("Camera pos", camera.Position.x, camera.Position.y, camera.Position.z);
            frameCount = 0;
            lastLogTime = currentFrame;
        }

        // input
        processInput(window);

        // Update animations
        animationManager.updateAnimation(deltaTime);

        // Render
        glClearColor(0.1f, 0.1f, 0.2f, 1.0f); // Darker blue background for better visibility
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        CHECK_OPENGL("Clear buffers");

        // activate shader
        ourShader.use();
        CHECK_OPENGL("Use shader");

        // pass projection matrix to shader
        glm::mat4 projection = glm::perspective(glm::radians(camera.Zoom), (float)SCR_WIDTH / (float)SCR_HEIGHT, 0.1f, 100.0f);
        ourShader.setMat4("projection", glm::value_ptr(projection));
        CHECK_OPENGL("Set projection matrix");

        // camera/view transformation
        glm::mat4 view = camera.GetViewMatrix();
        ourShader.setMat4("view", glm::value_ptr(view));
        CHECK_OPENGL("Set view matrix");

        // Log matrices on first frame for debugging
        static bool firstFrame = true;
        if (firstFrame) {
            LOG_MATRIX("Projection", glm::value_ptr(projection));
            LOG_MATRIX("View", glm::value_ptr(view));
            firstFrame = false;
        }

        // Set lighting uniforms
        glm::vec3 lightPos(1.2f, 1.0f, 2.0f);
        glm::vec3 lightColor(1.0f, 1.0f, 1.0f);
        glm::vec3 objectColor(1.0f, 0.5f, 0.31f);

        ourShader.setVec3("lightPos", lightPos);
        ourShader.setVec3("lightColor", lightColor);
        ourShader.setVec3("objectColor", objectColor);
        ourShader.setVec3("viewPos", camera.Position);

        // Set animation uniforms - support both skeletal and node-based animations
        bool hasAnimations = !gltfModel.animations.empty();
        bool hasSkins = !gltfModel.skins.empty();
        bool hasValidAnimations = hasAnimations;

        // Check if skeletal animation data is valid by looking at bone matrices
        bool hasValidSkeletalAnimation = false;
        if (hasValidAnimations && hasSkins) {
            const std::vector<glm::mat4>& boneMatrices = animationManager.getBoneMatrices();
            bool hasValidBones = false;
            for (const auto& matrix : boneMatrices) {
                // Check if matrix has reasonable values (not all zeros or huge numbers)
                bool matrixValid = true;
                for (int i = 0; i < 4 && matrixValid; ++i) {
                    for (int j = 0; j < 4 && matrixValid; ++j) {
                        float val = matrix[i][j];
                        if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1000.0f) {
                            matrixValid = false;
                        }
                    }
                }
                if (matrixValid) {
                    hasValidBones = true;
                    break;
                }
            }
            hasValidSkeletalAnimation = hasValidBones;
        }

        // Enable skeletal animation only if we have skins and valid bone matrices
        ourShader.setBool("useSkeletalAnimation", hasValidSkeletalAnimation);

        if (hasValidSkeletalAnimation) {
            const std::vector<glm::mat4>& boneMatrices = animationManager.getBoneMatrices();
            ourShader.setMat4Array("boneMatrices", boneMatrices);

            // Debug bone matrices on first frame
            static bool firstBoneFrame = true;
            if (firstBoneFrame) {
                LOG_INFO("Skeletal animation enabled with " + std::to_string(boneMatrices.size()) + " bone matrices");
                for (size_t i = 0; i < std::min(boneMatrices.size(), size_t(2)); ++i) {
                    LOG_MATRIX("Bone " + std::to_string(i), glm::value_ptr(boneMatrices[i]));
                }
                firstBoneFrame = false;
            }
        } else {
            if (hasAnimations && !hasSkins) {
                LOG_TRACE("Node-based animation active - no skeletal data needed");
            } else {
                LOG_TRACE("No animations available");
            }
        }

        // render the animated character
        glm::mat4 model = glm::mat4(1.0f);

        // Apply node-based animation transformations if available
        if (hasAnimations && !hasSkins) {
            // For node-based animations, we need to find the mesh node and apply its transformation
            // Look for the node that contains the mesh
            int meshNodeIndex = -1;
            for (size_t i = 0; i < gltfModel.nodes.size(); ++i) {
                if (gltfModel.nodes[i].mesh >= 0) {
                    meshNodeIndex = static_cast<int>(i);
                    break;
                }
            }

            if (meshNodeIndex >= 0) {
                glm::mat4 animatedTransform = animationManager.getNodeMatrix(meshNodeIndex);
                model = model * animatedTransform;

                // Log animated transform on first few frames
                static int animLogCount = 0;
                if (animLogCount < 3) {
                    LOG_INFO("Applying node-based animation transform for node " + std::to_string(meshNodeIndex));
                    LOG_MATRIX("Animated Transform", glm::value_ptr(animatedTransform));
                    animLogCount++;
                }
            }
        }

        // Position the character properly for visibility
        // The vertices are at Y=1.0-1.5, so center them at Y=0
        model = glm::translate(model, glm::vec3(0.0f, 0.0f, 0.0f)); // Move much lower to center at Y=0
        model = glm::scale(model, glm::vec3(4.0f, 4.0f, 4.0f)); // Make it much bigger for visibility
        // Optional rotation for better viewing
        model = glm::rotate(model, (makeRotation ? (float)glfwGetTime() * 0.5f : 0.0f), glm::vec3(0.0f, 1.0f, 0.0f));

        ourShader.setMat4("model", glm::value_ptr(model));
        CHECK_OPENGL("Set model matrix");

        // Set texture uniforms
        bool hasTexture = (gltfMesh.textureID != 0);
        ourShader.setBool("hasTexture", hasTexture);
        if (hasTexture) {
            ourShader.setInt("diffuseTexture", 0); // Texture unit 0
        }
        CHECK_OPENGL("Set texture uniforms");

        // Log model matrix on first frame
        static bool firstModelFrame = true;
        if (firstModelFrame) {
            LOG_MATRIX("Model", glm::value_ptr(model));
            firstModelFrame = false;
        }

        gltfMesh.Draw();
        CHECK_OPENGL("Draw mesh");

        // Validate render state after drawing (less spam)
        static int frameValidationCounter = 0;
        if (frameValidationCounter++ % 300 == 0) { // Every 5 seconds at 60fps
            Utils::ErrorHandler::getInstance().validateOpenGLState("After mesh draw", __FILE__, __LINE__);
        }

        glfwSwapBuffers(window);
        glfwPollEvents();
    }

    // glfw: terminate, clearing all previously allocated GLFW resources.
    LOG_INFO("Terminating GLFW and cleaning up resources...");

    // Print final statistics
    Utils::Logger::getInstance().printStatistics();
    Utils::ErrorHandler::getInstance().printErrorSummary();

    glfwDestroyWindow(window);
    glfwTerminate();

    LOG_INFO("=== Simple 3D Engine shutdown complete ===");
    return 0;
}

// process all input: query GLFW whether relevant keys are pressed/released this frame and react accordingly
void processInput(GLFWwindow *window) {
    if (glfwGetKey(window, GLFW_KEY_ESCAPE) == GLFW_PRESS)
        glfwSetWindowShouldClose(window, true);

    if (glfwGetKey(window, GLFW_KEY_W) == GLFW_PRESS)
        camera.ProcessKeyboard(FORWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_S) == GLFW_PRESS)
        camera.ProcessKeyboard(BACKWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_A) == GLFW_PRESS)
        camera.ProcessKeyboard(LEFT, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_D) == GLFW_PRESS)
        camera.ProcessKeyboard(RIGHT, deltaTime);
}

// glfw: whenever the window size changed (by OS or user resize) this callback function executes
void framebuffer_size_callback(GLFWwindow* window, int width, int height) {
    (void)window; // Suppress unused parameter warning
    glViewport(0, 0, width, height);
    LOG_INFO("Framebuffer resized to " + std::to_string(width) + "x" + std::to_string(height));
    CHECK_OPENGL("Viewport resize");
}

// Load texture from image file
GLuint loadTexture(const std::string& imagePath) {
    GLuint textureID;
    glGenTextures(1, &textureID);

    int width, height, nrChannels;

    // Try to load the image file
    unsigned char* data = stbi_load(imagePath.c_str(), &width, &height, &nrChannels, 0);

    if (data) {
        GLenum format;
        if (nrChannels == 1)
            format = GL_RED;
        else if (nrChannels == 3)
            format = GL_RGB;
        else if (nrChannels == 4)
            format = GL_RGBA;
        else {
            LOG_ERROR("Unsupported number of channels: " + std::to_string(nrChannels) + " in " + imagePath);
            stbi_image_free(data);
            return 0;
        }

        glBindTexture(GL_TEXTURE_2D, textureID);
        glTexImage2D(GL_TEXTURE_2D, 0, format, width, height, 0, format, GL_UNSIGNED_BYTE, data);
        glGenerateMipmap(GL_TEXTURE_2D);

        // Set texture parameters
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

        stbi_image_free(data);
        LOG_INFO("Successfully loaded texture: " + imagePath + " (" + std::to_string(width) + "x" + std::to_string(height) + ", " + std::to_string(nrChannels) + " channels)");

        return textureID;
    } else {
        LOG_ERROR("Failed to load texture: " + imagePath + " - " + std::string(stbi_failure_reason()));

        // Create a fallback procedural texture
        LOG_INFO("Creating fallback procedural texture for: " + imagePath);

        const int fallback_width = 256;
        const int fallback_height = 256;
        const int fallback_channels = 3;

        std::vector<unsigned char> fallback_data(fallback_width * fallback_height * fallback_channels);

        // Generate checkerboard pattern as fallback
        for (int y = 0; y < fallback_height; ++y) {
            for (int x = 0; x < fallback_width; ++x) {
                int index = (y * fallback_width + x) * fallback_channels;

                int checker_size = 32;
                int checker_x = (x / checker_size) % 2;
                int checker_y = (y / checker_size) % 2;

                if ((checker_x + checker_y) % 2 == 0) {
                    // Light blue squares
                    fallback_data[index + 0] = 100;  // R
                    fallback_data[index + 1] = 150;  // G
                    fallback_data[index + 2] = 255;  // B
                } else {
                    // Orange squares
                    fallback_data[index + 0] = 255;  // R
                    fallback_data[index + 1] = 150;  // G
                    fallback_data[index + 2] = 50;   // B
                }
            }
        }

        glBindTexture(GL_TEXTURE_2D, textureID);
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, fallback_width, fallback_height, 0, GL_RGB, GL_UNSIGNED_BYTE, fallback_data.data());
        glGenerateMipmap(GL_TEXTURE_2D);

        // Set texture parameters
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

        LOG_INFO("Created fallback procedural texture (" + std::to_string(fallback_width) + "x" + std::to_string(fallback_height) + ")");

        return textureID;
    }
}

// glfw: whenever the mouse moves, this callback is called
void mouse_callback(GLFWwindow* window, double xpos, double ypos) {
    (void)window; // Suppress unused parameter warning
    if (firstMouse) {
        lastX = xpos;
        lastY = ypos;
        firstMouse = false;
        LOG_TRACE("Mouse input initialized at (" + std::to_string(xpos) + ", " + std::to_string(ypos) + ")");
    }

    float xoffset = xpos - lastX;
    float yoffset = lastY - ypos; // reversed since y-coordinates go from bottom to top

    lastX = xpos;
    lastY = ypos;

    camera.ProcessMouseMovement(xoffset, yoffset);
}

// glfw: whenever the mouse scroll wheel scrolls, this callback is called
void scroll_callback(GLFWwindow* window, double xoffset, double yoffset) {
    (void)window; // Suppress unused parameter warning
    camera.ProcessMouseScroll(yoffset);
    LOG_TRACE("Mouse scrolled: x=" + std::to_string(xoffset) + ", y=" + std::to_string(yoffset) + ", zoom=" + std::to_string(camera.Zoom));
}
