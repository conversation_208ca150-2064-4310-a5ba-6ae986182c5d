#include <glad/glad.h>
#include <GLFW/glfw3.h>

#include <iostream>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

#include "shader.h"
#include "camera.h"
#include "mesh.h"
#include "gltf.h"
#include <fstream>
#include <sstream>

#include "logger.h"
#include "error_handler.h"

// Function to read file content into a string
std::string readFileIntoString(const std::string& path) {
    std::ifstream input_file(path);
    if (!input_file.is_open()) {
        Utils::ErrorHandler::getInstance().handleError("Could not open file " + path, false, __FILE__, __LINE__);
        return "";
    }
    std::stringstream sstr;
    sstr << input_file.rdbuf();
    return sstr.str();
}

// Callbacks
void framebuffer_size_callback(GLFWwindow* window, int width, int height);
void mouse_callback(GLFWwindow* window, double xpos, double ypos);
void scroll_callback(GLFWwindow* window, double xoffset, double yoffset);
void processInput(GLFWwindow *window);

// Settings
const unsigned int SCR_WIDTH = 800;
const unsigned int SCR_HEIGHT = 600;

// Camera
Camera camera(glm::vec3(0.0f, 0.0f, 3.0f));
float lastX = SCR_WIDTH / 2.0f;
float lastY = SCR_HEIGHT / 2.0f;
bool firstMouse = true;

// Timing
float deltaTime = 0.0f;
float lastFrame = 0.0f;

// Placeholder for error callback
void error_callback(int error, const char* description) {
    Utils::ErrorHandler::getInstance().handleError(std::string("GLFW Error (") + std::to_string(error) + "): " + description, false, __FILE__, __LINE__);
}

int main() {
    Utils::Logger::getInstance().setLogFile("engine.log");
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Starting Simple 3D Engine...");

    if (!glfwInit()) {
        Utils::ErrorHandler::getInstance().handleError("Failed to initialize GLFW", true);
        return -1;
    }

    glfwSetErrorCallback(error_callback);

    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    GLFWwindow* window = glfwCreateWindow(SCR_WIDTH, SCR_HEIGHT, "Simple 3D Engine", NULL, NULL);
    if (!window) {
        Utils::ErrorHandler::getInstance().handleError("Failed to create GLFW window", true);
        glfwTerminate();
        return -1;
    }

    Utils::Logger::getInstance().log(Utils::Logger::INFO, "GLFW window created successfully.");

    glfwMakeContextCurrent(window);
    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        Utils::ErrorHandler::getInstance().handleError("Failed to initialize GLAD", true, __FILE__, __LINE__);
        return -1;
    }
    glfwSetFramebufferSizeCallback(window, framebuffer_size_callback);
    glfwSetCursorPosCallback(window, mouse_callback);
    glfwSetScrollCallback(window, scroll_callback);

    // tell GLFW to capture our mouse
    glfwSetInputMode(window, GLFW_CURSOR, GLFW_CURSOR_DISABLED);

    // build and compile our shader program
    Shader ourShader("src/simple.vert", "src/simple.frag");
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Shader program compiled.");

    // Load and parse glTF file
    std::string gltfContent = readFileIntoString("test.gltf");
    if (gltfContent.empty()) {
        Utils::ErrorHandler::getInstance().handleError("Failed to read glTF file: test.gltf", true, __FILE__, __LINE__);
        return -1;
    }

    Gltf::GltfModel gltfModel;
    try {
        gltfModel = Gltf::GltfParser::parse(gltfContent, "./"); // Pass base directory for external files
        Utils::Logger::getInstance().log(Utils::Logger::INFO, "glTF file parsed successfully.", __FILE__, __LINE__);
    } catch (const std::runtime_error& e) {
        Utils::ErrorHandler::getInstance().handleError(std::string("Error parsing glTF: ") + e.what(), true, __FILE__, __LINE__);
        return -1;
    }

    // Assuming a single mesh and primitive for simplicity
    if (gltfModel.meshes.empty() || gltfModel.meshes[0].primitives.empty()) {
        Utils::ErrorHandler::getInstance().handleError("No mesh or primitive found in glTF model.", true, __FILE__, __LINE__);
        return -1;
    }

    const Gltf::Primitive& primitive = gltfModel.meshes[0].primitives[0];

    std::vector<Vertex> meshVertices;
    std::vector<GLuint> meshIndices;

    // Get position data
    if (primitive.attributes.count("POSITION")) {
        const Gltf::Accessor& posAccessor = gltfModel.accessors[primitive.attributes.at("POSITION")];
        const Gltf::BufferView& posBufferView = gltfModel.bufferViews[posAccessor.bufferView];
        const Gltf::Buffer& posBuffer = gltfModel.buffers[posBufferView.buffer];

        size_t vertexCount = posAccessor.count;
        meshVertices.resize(vertexCount);

        const float* positions = reinterpret_cast<const float*>(posBuffer.data.data() + posBufferView.byteOffset + posAccessor.byteOffset);

        for (size_t i = 0; i < vertexCount; ++i) {
            meshVertices[i].Position = glm::vec3(positions[i * 3], positions[i * 3 + 1], positions[i * 3 + 2]);
        }
    } else {
        Utils::ErrorHandler::getInstance().handleError("POSITION attribute not found in glTF primitive.", true, __FILE__, __LINE__);
        return -1;
    }

    // Get normal data
    if (primitive.attributes.count("NORMAL")) {
        const Gltf::Accessor& normalAccessor = gltfModel.accessors[primitive.attributes.at("NORMAL")];
        const Gltf::BufferView& normalBufferView = gltfModel.bufferViews[normalAccessor.bufferView];
        const Gltf::Buffer& normalBuffer = gltfModel.buffers[normalBufferView.buffer];

        const float* normals = reinterpret_cast<const float*>(normalBuffer.data.data() + normalBufferView.byteOffset + normalAccessor.byteOffset);

        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshVertices[i].Normal = glm::vec3(normals[i * 3], normals[i * 3 + 1], normals[i * 3 + 2]);
        }
    } else {
        Utils::Logger::getInstance().log(Utils::Logger::WARNING, "NORMAL attribute not found in glTF primitive. Normals will be default (0,0,0).", __FILE__, __LINE__);
    }

    // Get index data
    if (primitive.indices != -1) {
        const Gltf::Accessor& indexAccessor = gltfModel.accessors[primitive.indices];
        const Gltf::BufferView& indexBufferView = gltfModel.bufferViews[indexAccessor.bufferView];
        const Gltf::Buffer& indexBuffer = gltfModel.buffers[indexBufferView.buffer];

        size_t indexCount = indexAccessor.count;
        meshIndices.resize(indexCount);

        // Handle different component types for indices
        if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_SHORT) {
            const unsigned short* indices = reinterpret_cast<const unsigned short*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_INT) {
            const unsigned int* indices = reinterpret_cast<const unsigned int*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else if (indexAccessor.componentType == Gltf::ComponentType::UNSIGNED_BYTE) {
            const unsigned char* indices = reinterpret_cast<const unsigned char*>(indexBuffer.data.data() + indexBufferView.byteOffset + indexAccessor.byteOffset);
            for (size_t i = 0; i < indexCount; ++i) {
                meshIndices[i] = static_cast<GLuint>(indices[i]);
            }
        } else {
            Utils::ErrorHandler::getInstance().handleError("Unsupported index component type.", true, __FILE__, __LINE__);
            return -1;
        }
    } else {
        // If no indices, assume non-indexed drawing (e.g., triangles list)
        // For simplicity, we'll just use a sequential index buffer if none is provided
        for (size_t i = 0; i < meshVertices.size(); ++i) {
            meshIndices.push_back(static_cast<GLuint>(i));
        }
    }

    Mesh gltfMesh(meshVertices, meshIndices);
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Mesh loaded from glTF.", __FILE__, __LINE__);

    glEnable(GL_DEPTH_TEST);

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        // per-frame time logic
        float currentFrame = glfwGetTime();
        deltaTime = currentFrame - lastFrame;
        lastFrame = currentFrame;

        // input
        processInput(window);

        // Render
        glClearColor(0.2f, 0.3f, 0.3f, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // activate shader
        ourShader.use();

        // pass projection matrix to shader (note that in this case it could change every frame)
        glm::mat4 projection = glm::perspective(glm::radians(camera.Zoom), (float)SCR_WIDTH / (float)SCR_HEIGHT, 0.1f, 100.0f);
        ourShader.setMat4("projection", glm::value_ptr(projection));

        // camera/view transformation
        glm::mat4 view = camera.GetViewMatrix();
        ourShader.setMat4("view", glm::value_ptr(view));

        // render the glTF mesh
        glm::mat4 model = glm::mat4(1.0f);
        model = glm::translate(model, glm::vec3(0.0f, 0.0f, 0.0f)); // translate it down so it's at the center of the scene
        model = glm::rotate(model, (float)glfwGetTime(), glm::vec3(0.5f, 1.0f, 0.0f));
        ourShader.setMat4("model", glm::value_ptr(model));

        gltfMesh.Draw();

        glfwSwapBuffers(window);
        glfwPollEvents();
    }

    // glfw: terminate, clearing all previously allocated GLFW resources.
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Terminating GLFW and cleaning up resources.");
    glfwDestroyWindow(window);
    glfwTerminate();
    return 0;
}

// process all input: query GLFW whether relevant keys are pressed/released this frame and react accordingly
void processInput(GLFWwindow *window) {
    if (glfwGetKey(window, GLFW_KEY_ESCAPE) == GLFW_PRESS)
        glfwSetWindowShouldClose(window, true);

    if (glfwGetKey(window, GLFW_KEY_W) == GLFW_PRESS)
        camera.ProcessKeyboard(FORWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_S) == GLFW_PRESS)
        camera.ProcessKeyboard(BACKWARD, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_A) == GLFW_PRESS)
        camera.ProcessKeyboard(LEFT, deltaTime);
    if (glfwGetKey(window, GLFW_KEY_D) == GLFW_PRESS)
        camera.ProcessKeyboard(RIGHT, deltaTime);
}

// glfw: whenever the window size changed (by OS or user resize) this callback function executes
void framebuffer_size_callback(GLFWwindow* window, int width, int height) {
    // make sure the viewport matches the new window dimensions; note that width and
    // height will be significantly larger than specified on retina displays.
    glViewport(0, 0, width, height);
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Framebuffer resized to " + std::to_string(width) + "x" + std::to_string(height));
}

// glfw: whenever the mouse moves, this callback is called
void mouse_callback(GLFWwindow* window, double xpos, double ypos) {
    if (firstMouse) {
        lastX = xpos;
        lastY = ypos;
        firstMouse = false;
    }

    float xoffset = xpos - lastX;
    float yoffset = lastY - ypos; // reversed since y-coordinates go from bottom to top

    lastX = xpos;
    lastY = ypos;

    camera.ProcessMouseMovement(xoffset, yoffset);
}

// glfw: whenever the mouse scroll wheel scrolls, this callback is called
void scroll_callback(GLFWwindow* window, double xoffset, double yoffset) {
    camera.ProcessMouseScroll(yoffset);
    Utils::Logger::getInstance().log(Utils::Logger::INFO, "Mouse scrolled: x=" + std::to_string(xoffset) + ", y=" + std::to_string(yoffset));
}
