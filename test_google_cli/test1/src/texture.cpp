#include "texture.h"
#include "logger.h"
#include "error_handler.h"
#include <stb_image.h>
#include <iostream>

// Static member initialization
std::unordered_map<std::string, std::weak_ptr<Texture>> Texture::textureCache;

Texture::Texture() 
    : textureID(0), width(0), height(0), channels(0), format(TextureFormat::RGB) {
}

Texture::~Texture() {
    cleanup();
}

Texture::Texture(Texture&& other) noexcept 
    : textureID(other.textureID), width(other.width), height(other.height), 
      channels(other.channels), format(other.format), filePath(std::move(other.filePath)) {
    other.textureID = 0;
    other.width = 0;
    other.height = 0;
    other.channels = 0;
}

Texture& Texture::operator=(Texture&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        textureID = other.textureID;
        width = other.width;
        height = other.height;
        channels = other.channels;
        format = other.format;
        filePath = std::move(other.filePath);
        
        other.textureID = 0;
        other.width = 0;
        other.height = 0;
        other.channels = 0;
    }
    return *this;
}

bool Texture::loadFromFile(const std::string& filePath, bool flipVertically) {
    this->filePath = filePath;
    
    if (flipVertically) {
        stbi_set_flip_vertically_on_load(true);
    }
    
    bool success = loadImageData(filePath, flipVertically);
    
    if (flipVertically) {
        stbi_set_flip_vertically_on_load(false); // Reset to default
    }
    
    return success;
}

bool Texture::loadFromMemory(const unsigned char* data, int width, int height, TextureFormat format) {
    if (!data || width <= 0 || height <= 0) {
        LOG_ERROR("Invalid parameters for texture creation from memory");
        return false;
    }
    
    cleanup(); // Clean up any existing texture
    
    this->width = width;
    this->height = height;
    this->format = format;
    this->channels = (format == TextureFormat::RGBA) ? 4 : 3;
    
    glGenTextures(1, &textureID);
    glBindTexture(GL_TEXTURE_2D, textureID);
    
    glTexImage2D(GL_TEXTURE_2D, 0, static_cast<GLenum>(format), width, height, 0, 
                 static_cast<GLenum>(format), GL_UNSIGNED_BYTE, data);
    
    // Set default texture parameters
    setWrapMode(TextureWrap::REPEAT, TextureWrap::REPEAT);
    setFilterMode(TextureFilter::LINEAR_MIPMAP_LINEAR, TextureFilter::LINEAR);
    generateMipmaps();
    
    glBindTexture(GL_TEXTURE_2D, 0);
    
    if (Utils::ErrorHandler::getInstance().checkOpenGLError("Texture creation from memory")) {
        cleanup();
        return false;
    }
    
    LOG_INFO("Created texture from memory: " + std::to_string(width) + "x" + std::to_string(height));
    return true;
}

void Texture::bind(unsigned int textureUnit) const {
    if (textureID == 0) {
        LOG_WARNING("Attempting to bind invalid texture");
        return;
    }
    
    glActiveTexture(GL_TEXTURE0 + textureUnit);
    glBindTexture(GL_TEXTURE_2D, textureID);
}

void Texture::unbind() const {
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::setWrapMode(TextureWrap wrapS, TextureWrap wrapT) {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, static_cast<GLint>(wrapS));
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, static_cast<GLint>(wrapT));
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::setFilterMode(TextureFilter minFilter, TextureFilter magFilter) {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, static_cast<GLint>(minFilter));
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, static_cast<GLint>(magFilter));
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::generateMipmaps() {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glGenerateMipmap(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, 0);
}

std::shared_ptr<Texture> Texture::loadCached(const std::string& filePath, bool flipVertically) {
    // Check if texture is already in cache
    auto it = textureCache.find(filePath);
    if (it != textureCache.end()) {
        if (auto existingTexture = it->second.lock()) {
            LOG_INFO("Using cached texture: " + filePath);
            return existingTexture;
        } else {
            // Weak pointer expired, remove from cache
            textureCache.erase(it);
        }
    }
    
    // Create new texture
    auto texture = std::make_shared<Texture>();
    if (texture->loadFromFile(filePath, flipVertically)) {
        textureCache[filePath] = texture;
        LOG_INFO("Loaded and cached new texture: " + filePath);
        return texture;
    }
    
    LOG_ERROR("Failed to load texture: " + filePath);
    return nullptr;
}

void Texture::clearCache() {
    textureCache.clear();
    LOG_INFO("Texture cache cleared");
}

size_t Texture::getCacheSize() {
    // Clean up expired weak pointers
    for (auto it = textureCache.begin(); it != textureCache.end();) {
        if (it->second.expired()) {
            it = textureCache.erase(it);
        } else {
            ++it;
        }
    }
    return textureCache.size();
}

void Texture::cleanup() {
    if (textureID != 0) {
        glDeleteTextures(1, &textureID);
        textureID = 0;
        LOG_TRACE("Deleted texture ID: " + std::to_string(textureID));
    }
}

bool Texture::loadImageData(const std::string& filePath, bool flipVertically) {
    unsigned char* data = stbi_load(filePath.c_str(), &width, &height, &channels, 0);
    if (!data) {
        LOG_ERROR("Failed to load texture: " + filePath + " - " + std::string(stbi_failure_reason()));
        return false;
    }
    
    // Determine format based on channels
    format = static_cast<TextureFormat>(getDataFormat(channels));
    
    // Generate OpenGL texture
    glGenTextures(1, &textureID);
    glBindTexture(GL_TEXTURE_2D, textureID);
    
    glTexImage2D(GL_TEXTURE_2D, 0, getInternalFormat(channels), width, height, 0, 
                 getDataFormat(channels), GL_UNSIGNED_BYTE, data);
    
    // Set default texture parameters
    setWrapMode(TextureWrap::REPEAT, TextureWrap::REPEAT);
    setFilterMode(TextureFilter::LINEAR_MIPMAP_LINEAR, TextureFilter::LINEAR);
    generateMipmaps();
    
    glBindTexture(GL_TEXTURE_2D, 0);
    stbi_image_free(data);
    
    if (Utils::ErrorHandler::getInstance().checkOpenGLError("Texture loading")) {
        cleanup();
        return false;
    }
    
    LOG_INFO("Loaded texture: " + filePath + " (" + std::to_string(width) + "x" + 
             std::to_string(height) + ", " + std::to_string(channels) + " channels)");
    return true;
}

GLenum Texture::getInternalFormat(int channels) const {
    switch (channels) {
        case 1: return GL_RED;
        case 2: return GL_RG;
        case 3: return GL_RGB;
        case 4: return GL_RGBA;
        default: return GL_RGB;
    }
}

GLenum Texture::getDataFormat(int channels) const {
    switch (channels) {
        case 1: return GL_RED;
        case 2: return GL_RG;
        case 3: return GL_RGB;
        case 4: return GL_RGBA;
        default: return GL_RGB;
    }
}
