#include "texture.h"
#include "logger.h"
#include "error_handler.h"
#include <stb_image.h>
#include <GLFW/glfw3.h>
#include <iostream>
#include <fstream>

// Static member initialization
std::unordered_map<std::string, std::weak_ptr<Texture>> Texture::textureCache;

Texture::Texture() 
    : textureID(0), width(0), height(0), channels(0), format(TextureFormat::RGB) {
}

Texture::~Texture() {
    cleanup();
}

Texture::Texture(Texture&& other) noexcept 
    : textureID(other.textureID), width(other.width), height(other.height), 
      channels(other.channels), format(other.format), filePath(std::move(other.filePath)) {
    other.textureID = 0;
    other.width = 0;
    other.height = 0;
    other.channels = 0;
}

Texture& Texture::operator=(Texture&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        textureID = other.textureID;
        width = other.width;
        height = other.height;
        channels = other.channels;
        format = other.format;
        filePath = std::move(other.filePath);
        
        other.textureID = 0;
        other.width = 0;
        other.height = 0;
        other.channels = 0;
    }
    return *this;
}

bool Texture::loadFromFile(const std::string& filePath, bool flipVertically) {
    this->filePath = filePath;
    
    if (flipVertically) {
        stbi_set_flip_vertically_on_load(true);
    }
    
    bool success = loadImageData(filePath, flipVertically);
    
    if (flipVertically) {
        stbi_set_flip_vertically_on_load(false); // Reset to default
    }
    
    return success;
}

bool Texture::loadFromMemory(const unsigned char* data, int width, int height, TextureFormat format) {
    if (!data || width <= 0 || height <= 0) {
        LOG_ERROR("Invalid parameters for texture creation from memory");
        return false;
    }
    
    cleanup(); // Clean up any existing texture
    
    this->width = width;
    this->height = height;
    this->format = format;
    this->channels = (format == TextureFormat::RGBA) ? 4 : 3;
    
    glGenTextures(1, &textureID);
    glBindTexture(GL_TEXTURE_2D, textureID);
    
    glTexImage2D(GL_TEXTURE_2D, 0, static_cast<GLenum>(format), width, height, 0, 
                 static_cast<GLenum>(format), GL_UNSIGNED_BYTE, data);
    
    // Set default texture parameters
    setWrapMode(TextureWrap::REPEAT, TextureWrap::REPEAT);
    setFilterMode(TextureFilter::LINEAR_MIPMAP_LINEAR, TextureFilter::LINEAR);
    generateMipmaps();
    
    glBindTexture(GL_TEXTURE_2D, 0);
    
    if (Utils::ErrorHandler::getInstance().checkOpenGLError("Texture creation from memory")) {
        cleanup();
        return false;
    }
    
    LOG_INFO("Created texture from memory: " + std::to_string(width) + "x" + std::to_string(height));
    return true;
}

void Texture::bind(unsigned int textureUnit) const {
    if (textureID == 0) {
        LOG_WARNING("Attempting to bind invalid texture");
        return;
    }
    
    glActiveTexture(GL_TEXTURE0 + textureUnit);
    glBindTexture(GL_TEXTURE_2D, textureID);
}

void Texture::unbind() const {
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::setWrapMode(TextureWrap wrapS, TextureWrap wrapT) {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, static_cast<GLint>(wrapS));
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, static_cast<GLint>(wrapT));
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::setFilterMode(TextureFilter minFilter, TextureFilter magFilter) {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, static_cast<GLint>(minFilter));
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, static_cast<GLint>(magFilter));
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::generateMipmaps() {
    if (textureID == 0) return;
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glGenerateMipmap(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, 0);
}

std::shared_ptr<Texture> Texture::loadCached(const std::string& filePath, bool flipVertically) {
    // Check if texture is already in cache
    auto it = textureCache.find(filePath);
    if (it != textureCache.end()) {
        if (auto existingTexture = it->second.lock()) {
            LOG_INFO("Using cached texture: " + filePath);
            return existingTexture;
        } else {
            // Weak pointer expired, remove from cache
            textureCache.erase(it);
        }
    }
    
    // Create new texture
    auto texture = std::make_shared<Texture>();
    if (texture->loadFromFile(filePath, flipVertically)) {
        textureCache[filePath] = texture;
        LOG_INFO("Loaded and cached new texture: " + filePath);
        return texture;
    }
    
    LOG_ERROR("Failed to load texture: " + filePath);
    return nullptr;
}

void Texture::clearCache() {
    textureCache.clear();
    LOG_INFO("Texture cache cleared");
}

size_t Texture::getCacheSize() {
    // Clean up expired weak pointers
    for (auto it = textureCache.begin(); it != textureCache.end();) {
        if (it->second.expired()) {
            it = textureCache.erase(it);
        } else {
            ++it;
        }
    }
    return textureCache.size();
}

void Texture::cleanup() {
    if (textureID != 0) {
        glDeleteTextures(1, &textureID);
        textureID = 0;
        LOG_TRACE("Deleted texture ID: " + std::to_string(textureID));
    }
}

bool Texture::loadImageData(const std::string& filePath, bool flipVertically) {
    LOG_INFO("Attempting to load texture: " + filePath);

    // Check if file exists
    std::ifstream file(filePath);
    if (!file.good()) {
        LOG_ERROR("Texture file does not exist or cannot be opened: " + filePath);
        return false;
    }
    file.close();

    unsigned char* data = stbi_load(filePath.c_str(), &width, &height, &channels, 0);
    if (!data) {
        std::string reason = stbi_failure_reason() ? stbi_failure_reason() : "unknown error";
        LOG_ERROR("STBI failed to load texture: " + filePath + " - " + reason);
        return false;
    }

    LOG_INFO("STBI successfully loaded texture: " + filePath + " (" +
             std::to_string(width) + "x" + std::to_string(height) + ", " +
             std::to_string(channels) + " channels)");
    
    // Determine format based on channels
    format = static_cast<TextureFormat>(getDataFormat(channels));
    
    // Ensure we have a valid OpenGL context
    GLFWwindow* context = glfwGetCurrentContext();
    if (!context) {
        LOG_ERROR("No OpenGL context available for texture creation");
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("OpenGL context is valid, proceeding with texture creation");

    // Check OpenGL version and capabilities
    const GLubyte* version = glGetString(GL_VERSION);
    const GLubyte* vendor = glGetString(GL_VENDOR);
    const GLubyte* renderer = glGetString(GL_RENDERER);
    LOG_INFO("OpenGL Version: " + std::string(reinterpret_cast<const char*>(version)));
    LOG_INFO("OpenGL Vendor: " + std::string(reinterpret_cast<const char*>(vendor)));
    LOG_INFO("OpenGL Renderer: " + std::string(reinterpret_cast<const char*>(renderer)));

    // Clear any existing OpenGL errors before texture creation
    while (glGetError() != GL_NO_ERROR) {
        // Clear error queue
    }

    // Generate OpenGL texture
    LOG_INFO("Calling glGenTextures...");
    glGenTextures(1, &textureID);

    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        LOG_ERROR("glGenTextures failed with OpenGL error: " + std::to_string(error));
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Generated OpenGL texture ID: " + std::to_string(textureID));

    LOG_INFO("Binding texture ID " + std::to_string(textureID) + " to GL_TEXTURE_2D");
    glBindTexture(GL_TEXTURE_2D, textureID);

    GLenum bindError = glGetError();
    if (bindError != GL_NO_ERROR) {
        LOG_ERROR("glBindTexture failed with OpenGL error: " + std::to_string(bindError) +
                  " (GL_INVALID_ENUM=" + std::to_string(GL_INVALID_ENUM) +
                  ", GL_INVALID_VALUE=" + std::to_string(GL_INVALID_VALUE) +
                  ", GL_INVALID_OPERATION=" + std::to_string(GL_INVALID_OPERATION) + ")");
        glDeleteTextures(1, &textureID);
        textureID = 0;
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Successfully bound texture ID " + std::to_string(textureID));

    GLenum internalFormat = getInternalFormat(channels);
    GLenum dataFormat = getDataFormat(channels);

    LOG_INFO("Creating texture with format: internal=" + std::to_string(internalFormat) +
             ", data=" + std::to_string(dataFormat) + ", channels=" + std::to_string(channels));
    LOG_INFO("Texture dimensions: " + std::to_string(width) + "x" + std::to_string(height));
    LOG_INFO("Data pointer: " + std::to_string(reinterpret_cast<uintptr_t>(data)));

    glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width, height, 0,
                 dataFormat, GL_UNSIGNED_BYTE, data);

    GLenum texError = glGetError();
    if (texError != GL_NO_ERROR) {
        LOG_ERROR("glTexImage2D failed with OpenGL error: " + std::to_string(texError) +
                  " (GL_INVALID_ENUM=" + std::to_string(GL_INVALID_ENUM) +
                  ", GL_INVALID_VALUE=" + std::to_string(GL_INVALID_VALUE) +
                  ", GL_INVALID_OPERATION=" + std::to_string(GL_INVALID_OPERATION) +
                  ", GL_OUT_OF_MEMORY=" + std::to_string(GL_OUT_OF_MEMORY) + ")");
        cleanup();
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Successfully created texture data with glTexImage2D");

    // Set default texture parameters directly (texture is already bound)
    LOG_INFO("Setting texture wrap mode to REPEAT");
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    GLenum wrapSError = glGetError();
    if (wrapSError != GL_NO_ERROR) {
        LOG_ERROR("glTexParameteri(GL_TEXTURE_WRAP_S) failed with error: " + std::to_string(wrapSError));
        cleanup();
        stbi_image_free(data);
        return false;
    }

    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    GLenum wrapTError = glGetError();
    if (wrapTError != GL_NO_ERROR) {
        LOG_ERROR("glTexParameteri(GL_TEXTURE_WRAP_T) failed with error: " + std::to_string(wrapTError));
        cleanup();
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Successfully set texture wrap mode");

    LOG_INFO("Setting texture filter mode");
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
    GLenum minFilterError = glGetError();
    if (minFilterError != GL_NO_ERROR) {
        LOG_ERROR("glTexParameteri(GL_TEXTURE_MIN_FILTER) failed with error: " + std::to_string(minFilterError));
        cleanup();
        stbi_image_free(data);
        return false;
    }

    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    GLenum magFilterError = glGetError();
    if (magFilterError != GL_NO_ERROR) {
        LOG_ERROR("glTexParameteri(GL_TEXTURE_MAG_FILTER) failed with error: " + std::to_string(magFilterError));
        cleanup();
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Successfully set texture filter mode");

    LOG_INFO("Generating mipmaps");
    glGenerateMipmap(GL_TEXTURE_2D);
    GLenum mipmapError = glGetError();
    if (mipmapError != GL_NO_ERROR) {
        LOG_ERROR("glGenerateMipmap failed with error: " + std::to_string(mipmapError));
        cleanup();
        stbi_image_free(data);
        return false;
    }

    LOG_INFO("Successfully generated mipmaps");

    glBindTexture(GL_TEXTURE_2D, 0);
    stbi_image_free(data);
    
    LOG_INFO("Loaded texture: " + filePath + " (" + std::to_string(width) + "x" + 
             std::to_string(height) + ", " + std::to_string(channels) + " channels)");
    return true;
}

GLenum Texture::getInternalFormat(int channels) const {
    switch (channels) {
        case 1: return GL_RED;
        case 2: return GL_RG;
        case 3: return GL_RGB;
        case 4: return GL_RGBA;
        default: return GL_RGB;
    }
}

GLenum Texture::getDataFormat(int channels) const {
    switch (channels) {
        case 1: return GL_RED;
        case 2: return GL_RG;
        case 3: return GL_RGB;
        case 4: return GL_RGBA;
        default: return GL_RGB;
    }
}
