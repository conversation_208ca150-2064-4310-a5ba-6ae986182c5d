#version 330 core
in vec3 FragPos;
in vec3 Normal;
in vec2 TexCoord;

out vec4 FragColor;

uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 objectColor;
uniform vec3 viewPos;
uniform sampler2D diffuseTexture;
uniform bool hasTexture;

void main() {
    // Get base color from texture or use default
    vec3 baseColor;
    if (hasTexture) {
        vec4 texColor = texture(diffuseTexture, TexCoord);
        baseColor = texColor.rgb;
        // If texture has alpha, use it for transparency
        if (texColor.a < 0.1) {
            discard;
        }
    } else {
        // Fallback to bright yellow for visibility when no texture
        baseColor = vec3(1.0, 1.0, 0.0);
    }

    // Simple lighting
    float ambientStrength = 0.3;
    vec3 ambient = ambientStrength * lightColor;

    // Diffuse lighting
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(lightPos - FragPos);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * lightColor;

    // Combine lighting with base color
    vec3 result = (ambient + diffuse) * baseColor;

    // Ensure minimum brightness for visibility
    if (!hasTexture) {
        result = max(result, vec3(0.9, 0.9, 0.0));
    }

    FragColor = vec4(result, 1.0);
}