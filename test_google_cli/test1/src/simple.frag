#version 330 core
in vec3 FragPos;
in vec3 Normal;
in vec2 TexCoord;

out vec4 FragColor;

uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 objectColor;
uniform vec3 viewPos;

void main() {
    // For debugging: use bright white for maximum visibility
    vec3 debugColor = vec3(1.0, 1.0, 1.0); // Bright white

    // Simple lighting with bright base color
    float ambientStrength = 0.3;
    vec3 ambient = ambientStrength * lightColor;

    // Diffuse lighting
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(lightPos - FragPos);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * lightColor;

    // Combine with bright color for visibility
    vec3 result = (ambient + diffuse) * debugColor;

    // Ensure minimum brightness (bright white)
    result = max(result, vec3(0.8, 0.8, 0.8));

    FragColor = vec4(result, 1.0);
}