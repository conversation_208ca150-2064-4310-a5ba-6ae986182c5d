#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoord;
layout (location = 3) in vec4 aBoneWeights;
layout (location = 4) in ivec4 aBoneIndices;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
uniform bool useSkeletalAnimation;
uniform mat4 boneMatrices[100]; // Support up to 100 bones

out vec3 FragPos;
out vec3 Normal;
out vec2 TexCoord;

void main() {
    vec4 localPos = vec4(aPos, 1.0);
    vec3 localNormal = aNormal;

    // Apply skeletal animation if enabled
    if (useSkeletalAnimation) {
        mat4 boneTransform = mat4(0.0);

        // Blend up to 4 bone influences
        for (int i = 0; i < 4; i++) {
            if (aBoneWeights[i] > 0.0) {
                boneTransform += boneMatrices[aBoneIndices[i]] * aBoneWeights[i];
            }
        }

        // Apply bone transformation if any weights are present
        if (aBoneWeights.x + aBoneWeights.y + aBoneWeights.z + aBoneWeights.w > 0.0) {
            localPos = boneTransform * localPos;
            localNormal = mat3(boneTransform) * localNormal;
        }
    }

    FragPos = vec3(model * localPos);
    Normal = mat3(transpose(inverse(model))) * localNormal;
    TexCoord = aTexCoord;

    gl_Position = projection * view * vec4(FragPos, 1.0);
}