#include "json.h"
#include <iostream>
#include <algorithm> // For std::remove_if

// JsonValue implementations
JsonValue::JsonValue() : data(nullptr), type(NUL) {}

JsonValue::JsonValue(std::nullptr_t) : data(nullptr), type(NUL) {}

JsonValue::JsonValue(bool b) : data(b), type(BOOLEAN) {}

JsonValue::JsonValue(double n) : data(n), type(NUMBER) {}

JsonValue::JsonValue(int n) : data(static_cast<double>(n)), type(NUMBER) {}

JsonValue::JsonValue(const std::string& s) : data(s), type(STRING) {}

JsonValue::JsonValue(const char* s) : data(std::string(s)), type(STRING) {}

JsonValue::JsonValue(const JsonArray& arr) : data(arr), type(ARRAY) {}

JsonValue::JsonValue(const JsonObject& obj) : data(obj), type(OBJECT) {}

JsonValue::Type JsonValue::getType() const {
    return type;
}

bool JsonValue::asBool() const {
    if (!std::holds_alternative<bool>(data)) throw std::runtime_error("JsonValue is not a boolean");
    return std::get<bool>(data);
}

double JsonValue::asNumber() const {
    if (!std::holds_alternative<double>(data)) throw std::runtime_error("JsonValue is not a number");
    return std::get<double>(data);
}

const std::string& JsonValue::asString() const {
    if (!std::holds_alternative<std::string>(data)) throw std::runtime_error("JsonValue is not a string");
    return std::get<std::string>(data);
}

const JsonArray& JsonValue::asArray() const {
    if (!std::holds_alternative<JsonArray>(data)) throw std::runtime_error("JsonValue is not an array");
    return std::get<JsonArray>(data);
}

const JsonObject& JsonValue::asObject() const {
    if (!std::holds_alternative<JsonObject>(data)) throw std::runtime_error("JsonValue is not an object");
    return std::get<JsonObject>(data);
}

const JsonValue& JsonValue::operator[](const std::string& key) const {
    if (!std::holds_alternative<JsonObject>(data)) throw std::runtime_error("JsonValue is not an object");
    return std::get<JsonObject>(data).at(key);
}

JsonValue& JsonValue::operator[](const std::string& key) {
    if (!std::holds_alternative<JsonObject>(data)) throw std::runtime_error("JsonValue is not an object");
    return std::get<JsonObject>(data)[key];
}

const JsonValue& JsonValue::operator[](size_t index) const {
    if (!std::holds_alternative<JsonArray>(data)) throw std::runtime_error("JsonValue is not an array");
    return std::get<JsonArray>(data).at(index);
}

JsonValue& JsonValue::operator[](size_t index) {
    if (!std::holds_alternative<JsonArray>(data)) throw std::runtime_error("JsonValue is not an array");
    if (index >= std::get<JsonArray>(data).size()) {
        // Extend array with nulls if out of bounds
        std::get<JsonArray>(data).resize(index + 1);
    }
    return std::get<JsonArray>(data)[index];
}

// JsonParser implementations
JsonValue JsonParser::parse(const std::string& jsonString) {
    size_t index = 0;
    skipWhitespace(jsonString, index);
    return parseValue(jsonString, index);
}

void JsonParser::skipWhitespace(const std::string& jsonString, size_t& index) {
    while (index < jsonString.length() && std::isspace(jsonString[index])) {
        index++;
    }
}

JsonValue JsonParser::parseValue(const std::string& jsonString, size_t& index) {
    skipWhitespace(jsonString, index);
    if (index >= jsonString.length()) {
        throw std::runtime_error("Unexpected end of JSON string");
    }

    char current_char = jsonString[index];
    if (current_char == '"') {
        return JsonValue(parseString(jsonString, index));
    } else if (current_char == '{') {
        return JsonValue(parseObject(jsonString, index));
    } else if (current_char == '[') {
        return JsonValue(parseArray(jsonString, index));
    } else if (std::isdigit(current_char) || current_char == '-') {
        return JsonValue(parseNumber(jsonString, index));
    } else if (jsonString.substr(index, 4) == "true") {
        return parseBoolean(jsonString, index);
    } else if (jsonString.substr(index, 5) == "false") {
        return parseBoolean(jsonString, index);
    } else if (jsonString.substr(index, 4) == "null") {
        return parseNull(jsonString, index);
    } else {
        throw std::runtime_error("Unexpected character: " + std::string(1, current_char) + " at index " + std::to_string(index));
    }
}

std::string JsonParser::parseString(const std::string& jsonString, size_t& index) {
    index++; // Skip opening quote
    size_t start = index;
    std::string result = "";
    while (index < jsonString.length()) {
        char c = jsonString[index];
        if (c == '"') {
            result += jsonString.substr(start, index - start);
            index++; // Skip closing quote
            return result;
        } else if (c == '\\') {
            result += jsonString.substr(start, index - start);
            index++; // Skip backslash
            if (index >= jsonString.length()) throw std::runtime_error("Unexpected end of string after escape character");
            char escaped_char = jsonString[index];
            switch (escaped_char) {
                case '"': result += '"'; break;
                case '\\': result += '\\'; break;
                case '/': result += '/'; break;
                case 'b': result += '\b'; break;
                case 'f': result += '\f'; break;
                case 'n': result += '\n'; break;
                case 'r': result += '\r'; break;
                case 't': result += '\t'; break;
                case 'u':
                    // TODO: Handle unicode escapes
                    throw std::runtime_error("Unicode escapes not yet supported");
                default:
                    throw std::runtime_error("Invalid escape sequence: \\" + std::string(1, escaped_char));
            }
            index++;
            start = index;
        } else {
            index++;
        }
    }
    throw std::runtime_error("Unterminated string");
}

double JsonParser::parseNumber(const std::string& jsonString, size_t& index) {
    size_t start = index;
    while (index < jsonString.length() &&
           (std::isdigit(jsonString[index]) || jsonString[index] == '.' ||
            jsonString[index] == '-' || jsonString[index] == '+' ||
            jsonString[index] == 'e' || jsonString[index] == 'E')) {
        index++;
    }
    return std::stod(jsonString.substr(start, index - start));
}

bool JsonParser::parseBoolean(const std::string& jsonString, size_t& index) {
    if (jsonString.substr(index, 4) == "true") {
        index += 4;
        return true;
    } else if (jsonString.substr(index, 5) == "false") {
        index += 5;
        return false;
    }
    throw std::runtime_error("Invalid boolean value");
}

JsonValue JsonParser::parseNull(const std::string& jsonString, size_t& index) {
    if (jsonString.substr(index, 4) == "null") {
        index += 4;
        return JsonValue(nullptr);
    }
    throw std::runtime_error("Invalid null value");
}

JsonArray JsonParser::parseArray(const std::string& jsonString, size_t& index) {
    index++; // Skip opening '['
    JsonArray arr;
    skipWhitespace(jsonString, index);
    if (jsonString[index] == ']') {
        index++; // Skip closing ']'
        return arr;
    }

    while (index < jsonString.length()) {
        arr.push_back(parseValue(jsonString, index));
        skipWhitespace(jsonString, index);
        if (jsonString[index] == ']') {
            index++; // Skip closing ']'
            return arr;
        } else if (jsonString[index] == ',') {
            index++; // Skip comma
        } else {
            throw std::runtime_error("Expected ']' or ',' in array");
        }
    }
    throw std::runtime_error("Unterminated array");
}

JsonObject JsonParser::parseObject(const std::string& jsonString, size_t& index) {
    index++; // Skip opening '{'
    JsonObject obj;
    skipWhitespace(jsonString, index);
    if (jsonString[index] == '}') {
        index++; // Skip closing '}'
        return obj;
    }

    while (index < jsonString.length()) {
        skipWhitespace(jsonString, index);
        if (jsonString[index] != '"') {
            throw std::runtime_error("Expected string key in object");
        }
        std::string key = parseString(jsonString, index);
        skipWhitespace(jsonString, index);
        if (jsonString[index] != ':') {
            throw std::runtime_error("Expected ':' after key in object");
        }
        index++; // Skip ':'
        obj[key] = parseValue(jsonString, index);
        skipWhitespace(jsonString, index);
        if (jsonString[index] == '}') {
            index++; // Skip closing '}'
            return obj;
        } else if (jsonString[index] == ',') {
            index++; // Skip comma
        } else {
            throw std::runtime_error("Expected '}' or ',' in object");
        }
    }
    throw std::runtime_error("Unterminated object");
}
