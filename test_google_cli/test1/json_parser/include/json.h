#ifndef JSON_H
#define JSON_H

#include <string>
#include <vector>
#include <map>
#include <variant>
#include <stdexcept>

// Forward declarations
class JsonValue;

// Define the possible types for a JSON value
using JsonPrimitive = std::variant<
    std::nullptr_t,
    bool,
    double,
    std::string
>;

using JsonArray = std::vector<JsonValue>;
using JsonObject = std::map<std::string, JsonValue>;

class JsonValue {
public:
    enum Type {
        NUL,
        BOOLEAN,
        NUMBER,
        STRING,
        ARRAY,
        OBJECT
    };

    JsonValue();
    JsonValue(std::nullptr_t);
    JsonValue(bool b);
    JsonValue(double n);
    JsonValue(int n); // For integer literals
    JsonValue(const std::string& s);
    JsonValue(const char* s);
    JsonValue(const JsonArray& arr);
    JsonValue(const JsonObject& obj);

    Type getType() const;

    // Getters with type checking
    bool asBool() const;
    double asNumber() const;
    const std::string& asString() const;
    const JsonArray& asArray() const;
    const JsonObject& asObject() const;

    // Operator for object access
    const JsonValue& operator[](const std::string& key) const;
    JsonValue& operator[](const std::string& key);

    // Operator for array access
    const JsonValue& operator[](size_t index) const;
    JsonValue& operator[](size_t index);

private:
    std::variant<std::nullptr_t, bool, double, std::string, JsonArray, JsonObject> data;
    Type type;

    void setTypeFromVariant();
};

class JsonParser {
public:
    static JsonValue parse(const std::string& jsonString);

private:
    static JsonValue parseValue(const std::string& jsonString, size_t& index);
    static std::string parseString(const std::string& jsonString, size_t& index);
    static double parseNumber(const std::string& jsonString, size_t& index);
    static bool parseBoolean(const std::string& jsonString, size_t& index);
    static JsonValue parseNull(const std::string& jsonString, size_t& index);
    static JsonArray parseArray(const std::string& jsonString, size_t& index);
    static JsonObject parseObject(const std::string& jsonString, size_t& index);
    static void skipWhitespace(const std::string& jsonString, size_t& index);
};

#endif // JSON_H
