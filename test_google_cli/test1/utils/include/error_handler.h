#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <string>
#include "logger.h"
#include <functional>

namespace Utils {

class ErrorHandler {
public:
    static ErrorHandler& getInstance();

    void handleError(const std::string& message, bool fatal = false, const std::string& file = "", int line = 0);
    void setFatalErrorCallback(std::function<void()> callback);

private:
    Error<PERSON>andler();
    ErrorHandler(const ErrorHandler&) = delete;
    ErrorHandler& operator=(const ErrorHandler&) = delete;

    std::function<void()> fatalErrorCallback_;
};

} // namespace Utils

#endif // ERROR_HANDLER_H