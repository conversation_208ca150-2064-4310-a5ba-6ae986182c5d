#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <string>
#include <vector>
#include <chrono>
#include <functional>
#include <exception>
#include "logger.h"

namespace Utils {

class ErrorHandler {
public:
    enum ErrorCategory {
        OPENGL_ERROR,
        FILE_ERROR,
        MEMORY_ERROR,
        VALIDATION_ERROR,
        RENDERING_ERROR,
        ANIMATION_ERROR,
        GENERAL_ERROR
    };

    struct ErrorInfo {
        ErrorCategory category;
        std::string message;
        std::string file;
        int line;
        std::string function;
        std::chrono::system_clock::time_point timestamp;
        bool fatal;
        std::string stackTrace;
    };

    static ErrorHandler& getInstance();

    // Enhanced error handling
    void handleError(const std::string& message, bool fatal = false, const std::string& file = "", int line = 0, const std::string& function = "");
    void handleError(ErrorCategory category, const std::string& message, bool fatal = false, const std::string& file = "", int line = 0);

    // OpenGL specific error handling
    bool checkOpenGLError(const std::string& operation, const std::string& file = "", int line = 0);
    void validateOpenGLState(const std::string& context, const std::string& file = "", int line = 0);

    // File operation error handling
    void handleFileError(const std::string& filename, const std::string& operation, const std::string& file = "", int line = 0);

    // Validation helpers
    bool validatePointer(const void* ptr, const std::string& name, const std::string& file = "", int line = 0);
    bool validateRange(int value, int min, int max, const std::string& name, const std::string& file = "", int line = 0);
    bool validateArraySize(size_t size, size_t expected, const std::string& name, const std::string& file = "", int line = 0);

    // Configuration
    void setFatalErrorCallback(std::function<void()> callback);
    void enableStackTrace(bool enable);
    void setMaxErrorHistory(size_t maxErrors);

    // Error analysis
    std::vector<ErrorInfo> getRecentErrors(size_t count = 10);
    void printErrorSummary();
    size_t getErrorCount(ErrorCategory category = GENERAL_ERROR);

    // Exception handling
    void handleException(const std::exception& e, const std::string& context, const std::string& file = "", int line = 0);

private:
    ErrorHandler();
    ErrorHandler(const ErrorHandler&) = delete;
    ErrorHandler& operator=(const ErrorHandler&) = delete;

    void logError(const ErrorInfo& error);
    std::string getStackTrace();
    std::string categoryToString(ErrorCategory category);

    std::function<void()> fatalErrorCallback_;
    std::vector<ErrorInfo> errorHistory_;
    size_t maxErrorHistory_;
    bool stackTraceEnabled_;
    std::vector<size_t> categoryCounts_;
};

// Convenience macros
#define HANDLE_ERROR(msg, fatal) Utils::ErrorHandler::getInstance().handleError(msg, fatal, __FILE__, __LINE__, __FUNCTION__)
#define CHECK_OPENGL(op) Utils::ErrorHandler::getInstance().checkOpenGLError(op, __FILE__, __LINE__)
#define VALIDATE_PTR(ptr, name) Utils::ErrorHandler::getInstance().validatePointer(ptr, name, __FILE__, __LINE__)
#define VALIDATE_RANGE(val, min, max, name) Utils::ErrorHandler::getInstance().validateRange(val, min, max, name, __FILE__, __LINE__)
#define HANDLE_EXCEPTION(e, ctx) Utils::ErrorHandler::getInstance().handleException(e, ctx, __FILE__, __LINE__)

} // namespace Utils

#endif // ERROR_HANDLER_H