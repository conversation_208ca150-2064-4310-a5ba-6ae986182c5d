#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <fstream>
#include <mutex>
#include <chrono>
#include <iomanip>

namespace Utils {

class Logger {
public:
    enum LogLevel {
        TRACE,
        DEBUG,
        INFO,
        WARNING,
        ERROR,
        FATAL
    };

    static Logger& getInstance();

    void log(LogLevel level, const std::string& message, const std::string& file = "", int line = 0);
    void setLogFile(const std::string& filename);
    void setMinLevel(LogLevel level);
    void enableConsoleOutput(bool enable);
    void enableFileOutput(bool enable);

private:
    Logger();
    ~Logger();
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    std::ofstream logFile_;
    std::mutex mutex_;
    LogLevel minLevel_;
    bool consoleOutputEnabled_;
    bool fileOutputEnabled_;

    std::string getTimestamp();
    std::string logLevelToString(LogLevel level);
};

} // namespace Utils

#endif // LOGGER_H