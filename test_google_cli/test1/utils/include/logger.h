#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <fstream>
#include <mutex>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <vector>
#include <memory>

namespace Utils {

class Logger {
public:
    enum LogLevel {
        TRACE = 0,
        DEBUG = 1,
        INFO = 2,
        WARNING = 3,
        ERROR = 4,
        FATAL = 5
    };

    struct LogEntry {
        LogLevel level;
        std::string message;
        std::string file;
        int line;
        std::chrono::system_clock::time_point timestamp;
        std::string function;
        std::string category;
    };

    static Logger& getInstance();

    // Enhanced logging methods
    void log(LogLevel level, const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void logWithCategory(LogLevel level, const std::string& category, const std::string& message, const std::string& file = "", int line = 0);

    // Specialized logging methods
    void trace(const std::string& message, const std::string& file = "", int line = 0);
    void debug(const std::string& message, const std::string& file = "", int line = 0);
    void info(const std::string& message, const std::string& file = "", int line = 0);
    void warning(const std::string& message, const std::string& file = "", int line = 0);
    void error(const std::string& message, const std::string& file = "", int line = 0);
    void fatal(const std::string& message, const std::string& file = "", int line = 0);

    // OpenGL specific logging
    void logOpenGL(const std::string& operation, const std::string& file = "", int line = 0);
    void logMatrix(const std::string& name, const float* matrix, const std::string& file = "", int line = 0);
    void logVector3(const std::string& name, float x, float y, float z, const std::string& file = "", int line = 0);
    void logRenderState(const std::string& file = "", int line = 0);

    // Configuration
    void setLogFile(const std::string& filename);
    void setTraceFile(const std::string& filename);
    void setMinLevel(LogLevel level);
    void enableConsoleOutput(bool enable);
    void enableFileOutput(bool enable);
    void enableColors(bool enable);
    void enableTimestamps(bool enable);
    void enableFileInfo(bool enable);

    // Statistics and analysis
    void printStatistics();
    std::vector<LogEntry> getRecentEntries(size_t count = 100);
    void flushAll();

private:
    Logger();
    ~Logger();
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    void writeToOutputs(const LogEntry& entry);
    std::string formatLogEntry(const LogEntry& entry, bool useColors = false);
    std::string getTimestamp(const std::chrono::system_clock::time_point& time);
    std::string logLevelToString(LogLevel level);
    std::string getColorCode(LogLevel level);
    std::string getResetColorCode();

    std::ofstream logFile_;
    std::ofstream traceFile_;
    std::mutex mutex_;
    LogLevel minLevel_;
    bool consoleOutputEnabled_;
    bool fileOutputEnabled_;
    bool colorsEnabled_;
    bool timestampsEnabled_;
    bool fileInfoEnabled_;

    std::vector<LogEntry> recentEntries_;
    size_t maxRecentEntries_;

    // Statistics
    std::vector<size_t> levelCounts_;
};

// Convenience macros for easier logging with automatic file/line info
#define LOG_TRACE(msg) Utils::Logger::getInstance().trace(msg, __FILE__, __LINE__)
#define LOG_DEBUG(msg) Utils::Logger::getInstance().debug(msg, __FILE__, __LINE__)
#define LOG_INFO(msg) Utils::Logger::getInstance().info(msg, __FILE__, __LINE__)
#define LOG_WARNING(msg) Utils::Logger::getInstance().warning(msg, __FILE__, __LINE__)
#define LOG_ERROR(msg) Utils::Logger::getInstance().error(msg, __FILE__, __LINE__)
#define LOG_FATAL(msg) Utils::Logger::getInstance().fatal(msg, __FILE__, __LINE__)

#define LOG_OPENGL(op) Utils::Logger::getInstance().logOpenGL(op, __FILE__, __LINE__)
#define LOG_MATRIX(name, matrix) Utils::Logger::getInstance().logMatrix(name, matrix, __FILE__, __LINE__)
#define LOG_VECTOR3(name, x, y, z) Utils::Logger::getInstance().logVector3(name, x, y, z, __FILE__, __LINE__)
#define LOG_RENDER_STATE() Utils::Logger::getInstance().logRenderState(__FILE__, __LINE__)

} // namespace Utils

#endif // LOGGER_H