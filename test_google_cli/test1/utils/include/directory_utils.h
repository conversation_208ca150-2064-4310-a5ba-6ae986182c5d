#ifndef DIRECTORY_UTILS_H
#define DIRECTORY_UTILS_H

#include <string>
#include <vector>
#include <optional> // For std::optional

namespace Utils {
    // Function to list files in a given directory
    std::vector<std::string> list_files_in_directory(const std::string& path);

    // Function to read a JSON file and return its content as a string
    std::optional<std::string> read_json_file(const std::string& file_path);

} // namespace Utils

#endif // DIRECTORY_UTILS_H
