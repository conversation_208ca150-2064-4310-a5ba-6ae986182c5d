#include "error_handler.h"
#include <iostream>
#include <cstdlib> // For exit

namespace Utils {

<PERSON>rrorHandler& ErrorHandler::getInstance() {
    static ErrorHandler instance;
    return instance;
}

ErrorHandler::Error<PERSON>andler() {
    // Default fatal error callback: exit the application
    fatalErrorCallback_ = []() { 
        std::cerr << "Fatal error occurred. Exiting application." << std::endl;
        exit(EXIT_FAILURE); 
    };
}

void ErrorHandler::handleError(const std::string& message, bool fatal, const std::string& file, int line) {
    Logger::getInstance().log(Logger::ERROR, "Error: " + message, file, line);
    if (fatal) {
        Logger::getInstance().log(Logger::FATAL, "Fatal Error: " + message, file, line);
        if (fatalErrorCallback_) {
            fatalErrorCallback_();
        }
    }
}

void ErrorHandler::setFatalErrorCallback(std::function<void()> callback) {
    fatalErrorCallback_ = callback;
}

} // namespace Utils