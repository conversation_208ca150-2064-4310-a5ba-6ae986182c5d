#include "error_handler.h"
#include <iostream>
#include <cstdlib>
#include <sstream>
#include <glad/glad.h>

#ifdef __linux__
#include <execinfo.h>
#include <cxxabi.h>
#endif

namespace Utils {

ErrorHandler& ErrorHandler::getInstance() {
    static <PERSON>rror<PERSON>andler instance;
    return instance;
}

ErrorHandler::ErrorHandler()
    : maxErrorHistory_(100),
      stackTraceEnabled_(true),
      categoryCounts_(7, 0) {

    // Default fatal error callback
    fatalErrorCallback_ = []() {
        Logger::getInstance().fatal("Fatal error occurred. Exiting application.");
        Logger::getInstance().printStatistics();
        exit(EXIT_FAILURE);
    };

    errorHistory_.reserve(maxErrorHistory_);
}

void ErrorHandler::handleError(const std::string& message, bool fatal, const std::string& file, int line, const std::string& function) {
    ErrorInfo error;
    error.category = GENERAL_ERROR;
    error.message = message;
    error.file = file;
    error.line = line;
    error.function = function;
    error.timestamp = std::chrono::system_clock::now();
    error.fatal = fatal;

    if (stackTraceEnabled_) {
        error.stackTrace = getStackTrace();
    }

    logError(error);

    // Store in history
    errorHistory_.push_back(error);
    if (errorHistory_.size() > maxErrorHistory_) {
        errorHistory_.erase(errorHistory_.begin());
    }

    categoryCounts_[error.category]++;

    if (fatal && fatalErrorCallback_) {
        fatalErrorCallback_();
    }
}

void ErrorHandler::handleError(ErrorCategory category, const std::string& message, bool fatal, const std::string& file, int line) {
    ErrorInfo error;
    error.category = category;
    error.message = message;
    error.file = file;
    error.line = line;
    error.timestamp = std::chrono::system_clock::now();
    error.fatal = fatal;

    if (stackTraceEnabled_) {
        error.stackTrace = getStackTrace();
    }

    logError(error);

    errorHistory_.push_back(error);
    if (errorHistory_.size() > maxErrorHistory_) {
        errorHistory_.erase(errorHistory_.begin());
    }

    categoryCounts_[category]++;

    if (fatal && fatalErrorCallback_) {
        fatalErrorCallback_();
    }
}

bool ErrorHandler::checkOpenGLError(const std::string& operation, const std::string& file, int line) {
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        std::string errorMsg = "OpenGL Error in " + operation + ": ";
        switch (error) {
            case GL_INVALID_ENUM: errorMsg += "GL_INVALID_ENUM"; break;
            case GL_INVALID_VALUE: errorMsg += "GL_INVALID_VALUE"; break;
            case GL_INVALID_OPERATION: errorMsg += "GL_INVALID_OPERATION"; break;
            case GL_OUT_OF_MEMORY: errorMsg += "GL_OUT_OF_MEMORY"; break;
            case GL_INVALID_FRAMEBUFFER_OPERATION: errorMsg += "GL_INVALID_FRAMEBUFFER_OPERATION"; break;
            default: errorMsg += "Unknown error " + std::to_string(error); break;
        }
        handleError(OPENGL_ERROR, errorMsg, false, file, line);
        return false;
    }
    return true;
}

void ErrorHandler::validateOpenGLState(const std::string& context, const std::string& file, int line) {
    GLint currentProgram;
    glGetIntegerv(GL_CURRENT_PROGRAM, &currentProgram);

    GLint currentVAO;
    glGetIntegerv(GL_VERTEX_ARRAY_BINDING, &currentVAO);

    std::stringstream ss;
    ss << "OpenGL State Validation [" << context << "]:\n";
    ss << "  Current Program: " << currentProgram << "\n";
    ss << "  Current VAO: " << currentVAO << "\n";

    GLboolean depthTest;
    glGetBooleanv(GL_DEPTH_TEST, &depthTest);
    ss << "  Depth Test: " << (depthTest ? "Enabled" : "Disabled") << "\n";

    Logger::getInstance().trace(ss.str(), file, line);

    if (currentProgram == 0) {
        handleError(RENDERING_ERROR, "No shader program bound during " + context, false, file, line);
    }

    if (currentVAO == 0) {
        handleError(RENDERING_ERROR, "No VAO bound during " + context, false, file, line);
    }
}

void ErrorHandler::handleFileError(const std::string& filename, const std::string& operation, const std::string& file, int line) {
    std::string message = "File operation failed: " + operation + " on file '" + filename + "'";
    handleError(FILE_ERROR, message, false, file, line);
}

bool ErrorHandler::validatePointer(const void* ptr, const std::string& name, const std::string& file, int line) {
    if (ptr == nullptr) {
        handleError(VALIDATION_ERROR, "Null pointer: " + name, false, file, line);
        return false;
    }
    return true;
}

bool ErrorHandler::validateRange(int value, int min, int max, const std::string& name, const std::string& file, int line) {
    if (value < min || value > max) {
        std::string message = name + " value " + std::to_string(value) + " is out of range [" +
                             std::to_string(min) + ", " + std::to_string(max) + "]";
        handleError(VALIDATION_ERROR, message, false, file, line);
        return false;
    }
    return true;
}

bool ErrorHandler::validateArraySize(size_t size, size_t expected, const std::string& name, const std::string& file, int line) {
    if (size != expected) {
        std::string message = name + " array size " + std::to_string(size) + " does not match expected size " + std::to_string(expected);
        handleError(VALIDATION_ERROR, message, false, file, line);
        return false;
    }
    return true;
}

void ErrorHandler::handleException(const std::exception& e, const std::string& context, const std::string& file, int line) {
    std::string message = "Exception in " + context + ": " + e.what();
    handleError(GENERAL_ERROR, message, false, file, line);
}

void ErrorHandler::logError(const ErrorInfo& error) {
    std::string prefix = "[" + categoryToString(error.category) + "] ";

    if (error.fatal) {
        Logger::getInstance().fatal(prefix + error.message, error.file, error.line);
    } else {
        Logger::getInstance().error(prefix + error.message, error.file, error.line);
    }

    if (!error.stackTrace.empty()) {
        Logger::getInstance().trace("Stack trace:\n" + error.stackTrace, error.file, error.line);
    }
}

std::string ErrorHandler::getStackTrace() {
#ifdef __linux__
    void *array[20];
    size_t size = backtrace(array, 20);
    char **strings = backtrace_symbols(array, size);

    std::stringstream ss;
    for (size_t i = 1; i < size; ++i) { // Skip the first frame (this function)
        ss << "  " << strings[i] << "\n";
    }

    free(strings);
    return ss.str();
#else
    return "Stack trace not available on this platform";
#endif
}

std::string ErrorHandler::categoryToString(ErrorCategory category) {
    switch (category) {
        case OPENGL_ERROR: return "OpenGL";
        case FILE_ERROR: return "File";
        case MEMORY_ERROR: return "Memory";
        case VALIDATION_ERROR: return "Validation";
        case RENDERING_ERROR: return "Rendering";
        case ANIMATION_ERROR: return "Animation";
        case GENERAL_ERROR: return "General";
        default: return "Unknown";
    }
}

void ErrorHandler::setFatalErrorCallback(std::function<void()> callback) {
    fatalErrorCallback_ = callback;
}

void ErrorHandler::enableStackTrace(bool enable) {
    stackTraceEnabled_ = enable;
}

void ErrorHandler::setMaxErrorHistory(size_t maxErrors) {
    maxErrorHistory_ = maxErrors;
    if (errorHistory_.size() > maxErrorHistory_) {
        errorHistory_.resize(maxErrorHistory_);
    }
}

std::vector<ErrorHandler::ErrorInfo> ErrorHandler::getRecentErrors(size_t count) {
    if (count >= errorHistory_.size()) {
        return errorHistory_;
    }
    return std::vector<ErrorInfo>(errorHistory_.end() - count, errorHistory_.end());
}

void ErrorHandler::printErrorSummary() {
    std::cout << "\n" << std::string(60, '=') << "\n";
    std::cout << "Error Handler Summary:\n";
    std::cout << std::string(60, '=') << "\n";

    const char* categoryNames[] = {"OpenGL", "File", "Memory", "Validation", "Rendering", "Animation", "General"};
    for (size_t i = 0; i < categoryCounts_.size(); ++i) {
        if (categoryCounts_[i] > 0) {
            std::cout << categoryNames[i] << " errors: " << categoryCounts_[i] << "\n";
        }
    }

    std::cout << "Total errors in history: " << errorHistory_.size() << "\n";
    std::cout << std::string(60, '=') << "\n\n";
}

size_t ErrorHandler::getErrorCount(ErrorCategory category) {
    if (category < categoryCounts_.size()) {
        return categoryCounts_[category];
    }
    return 0;
}

} // namespace Utils