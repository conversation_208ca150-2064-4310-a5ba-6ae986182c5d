#include "directory_utils.h"
#include <filesystem>
#include <fstream>
#include <iostream>

namespace Utils {

    std::vector<std::string> list_files_in_directory(const std::string& path) {
        std::vector<std::string> files;
        try {
            for (const auto& entry : std::filesystem::directory_iterator(path)) {
                if (std::filesystem::is_regular_file(entry.status())) {
                    files.push_back(entry.path().filename().string());
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            std::cerr << "Error listing directory " << path << ": " << e.what() << std::endl;
        }
        return files;
    }

    std::optional<std::string> read_json_file(const std::string& file_path) {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            std::cerr << "Error: Could not open file " << file_path << std::endl;
            return std::nullopt;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                            std::istreambuf_iterator<char>());
        return content;
    }

} // namespace Utils
