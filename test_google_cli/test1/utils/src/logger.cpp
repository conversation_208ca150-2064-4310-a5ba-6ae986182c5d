#include "logger.h"
#include <iostream>
#include <cctype> // For isalnum

namespace Utils {

Logger& Logger::getInstance() {
    static Logger instance;
    return instance;
}

Logger::Logger()
    : minLevel_(INFO),
      consoleOutputEnabled_(true),
      fileOutputEnabled_(false) {
    // Default log to console
}

Logger::~Logger() {
    if (logFile_.is_open()) {
        logFile_.close();
    }
}

void Logger::setLogFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logFile_.is_open()) {
        logFile_.close();
    }
    logFile_.open(filename, std::ios::app);
    if (!logFile_.is_open()) {
        std::cerr << "Warning: Could not open log file: " << filename << std::endl;
    } else {
        fileOutputEnabled_ = true;
    }
}

void Logger::setMinLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    minLevel_ = level;
}

void Logger::enableConsoleOutput(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    consoleOutputEnabled_ = enable;
}

void Logger::enableFileOutput(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    fileOutputEnabled_ = enable;
}

void Logger::log(LogLevel level, const std::string& message, const std::string& file, int line) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (level < minLevel_) {
        return; // Do not log if level is below minimum
    }

    std::string formattedMessage = "[" + getTimestamp() + "] [" + logLevelToString(level) + "] ";
    if (!file.empty()) {
        formattedMessage += "(" + file + ":" + std::to_string(line) + ") ";
    }
    formattedMessage += message;

    if (consoleOutputEnabled_) {
        std::cout << formattedMessage << std::endl; // Log to console
    }

    if (fileOutputEnabled_ && logFile_.is_open()) {
        logFile_ << formattedMessage << std::endl; // Log to file
    }
}

std::string Logger::getTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

std::string Logger::logLevelToString(LogLevel level) {
    switch (level) {
        case TRACE: return "TRACE";
        case DEBUG: return "DEBUG";
        case INFO: return "INFO";
        case WARNING: return "WARNING";
        case ERROR: return "ERROR";
        case FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

} // namespace Utils