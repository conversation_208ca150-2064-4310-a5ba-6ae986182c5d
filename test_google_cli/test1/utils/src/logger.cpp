#include "logger.h"
#include <iostream>
#include <cctype>
#include <glad/glad.h>
#include <iomanip>
#include <algorithm>

namespace Utils {

Lo<PERSON>& Logger::getInstance() {
    static Logger instance;
    return instance;
}

Logger::Logger()
    : minLevel_(TRACE),
      consoleOutputEnabled_(true),
      fileOutputEnabled_(false),
      colorsEnabled_(true),
      timestampsEnabled_(true),
      fileInfoEnabled_(true),
      maxRecentEntries_(1000),
      levelCounts_(6, 0) {
    recentEntries_.reserve(maxRecentEntries_);
}

Logger::~Logger() {
    flushAll();
    if (logFile_.is_open()) {
        logFile_.close();
    }
    if (traceFile_.is_open()) {
        traceFile_.close();
    }
}

void Logger::setLogFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logFile_.is_open()) {
        logFile_.close();
    }
    logFile_.open(filename, std::ios::app);
    if (!logFile_.is_open()) {
        std::cerr << "Warning: Could not open log file: " << filename << std::endl;
    } else {
        fileOutputEnabled_ = true;
        // Write header to log file
        logFile_ << "\n" << std::string(80, '=') << "\n";
        logFile_ << "Simple 3D Engine Log - Session Started: " << getTimestamp(std::chrono::system_clock::now()) << "\n";
        logFile_ << std::string(80, '=') << "\n\n";
        logFile_.flush();
    }
}

void Logger::setTraceFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (traceFile_.is_open()) {
        traceFile_.close();
    }
    traceFile_.open(filename, std::ios::app);
    if (traceFile_.is_open()) {
        traceFile_ << "\n" << std::string(80, '-') << "\n";
        traceFile_ << "Trace Log - Session Started: " << getTimestamp(std::chrono::system_clock::now()) << "\n";
        traceFile_ << std::string(80, '-') << "\n\n";
        traceFile_.flush();
    }
}

void Logger::setMinLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    minLevel_ = level;
}

void Logger::enableConsoleOutput(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    consoleOutputEnabled_ = enable;
}

void Logger::enableFileOutput(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    fileOutputEnabled_ = enable;
}

void Logger::log(LogLevel level, const std::string& message, const std::string& file, int line, const std::string& function) {
    if (level < minLevel_) {
        return;
    }

    LogEntry entry;
    entry.level = level;
    entry.message = message;
    entry.file = file;
    entry.line = line;
    entry.function = function;
    entry.timestamp = std::chrono::system_clock::now();

    std::lock_guard<std::mutex> lock(mutex_);

    // Update statistics
    if (level < levelCounts_.size()) {
        levelCounts_[level]++;
    }

    // Store in recent entries
    recentEntries_.push_back(entry);
    if (recentEntries_.size() > maxRecentEntries_) {
        recentEntries_.erase(recentEntries_.begin());
    }

    writeToOutputs(entry);
}

void Logger::logWithCategory(LogLevel level, const std::string& category, const std::string& message, const std::string& file, int line) {
    LogEntry entry;
    entry.level = level;
    entry.message = message;
    entry.file = file;
    entry.line = line;
    entry.category = category;
    entry.timestamp = std::chrono::system_clock::now();

    std::lock_guard<std::mutex> lock(mutex_);

    if (level < minLevel_) {
        return;
    }

    levelCounts_[level]++;
    recentEntries_.push_back(entry);
    if (recentEntries_.size() > maxRecentEntries_) {
        recentEntries_.erase(recentEntries_.begin());
    }

    writeToOutputs(entry);
}

// Specialized logging methods
void Logger::trace(const std::string& message, const std::string& file, int line) {
    log(TRACE, message, file, line);

    // Also write to trace file if available
    if (traceFile_.is_open()) {
        std::lock_guard<std::mutex> lock(mutex_);
        traceFile_ << "[TRACE] " << getTimestamp(std::chrono::system_clock::now()) << " ";
        if (!file.empty()) {
            traceFile_ << "(" << file << ":" << line << ") ";
        }
        traceFile_ << message << std::endl;
        traceFile_.flush();
    }
}

void Logger::debug(const std::string& message, const std::string& file, int line) {
    log(DEBUG, message, file, line);
}

void Logger::info(const std::string& message, const std::string& file, int line) {
    log(INFO, message, file, line);
}

void Logger::warning(const std::string& message, const std::string& file, int line) {
    log(WARNING, message, file, line);
}

void Logger::error(const std::string& message, const std::string& file, int line) {
    log(ERROR, message, file, line);
}

void Logger::fatal(const std::string& message, const std::string& file, int line) {
    log(FATAL, message, file, line);
    flushAll(); // Ensure fatal messages are written immediately
}

// OpenGL specific logging
void Logger::logOpenGL(const std::string& operation, const std::string& file, int line) {
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        std::string errorMsg = "OpenGL Error in " + operation + ": ";
        switch (error) {
            case GL_INVALID_ENUM: errorMsg += "GL_INVALID_ENUM"; break;
            case GL_INVALID_VALUE: errorMsg += "GL_INVALID_VALUE"; break;
            case GL_INVALID_OPERATION: errorMsg += "GL_INVALID_OPERATION"; break;
            case GL_OUT_OF_MEMORY: errorMsg += "GL_OUT_OF_MEMORY"; break;
            case GL_INVALID_FRAMEBUFFER_OPERATION: errorMsg += "GL_INVALID_FRAMEBUFFER_OPERATION"; break;
            default: errorMsg += "Unknown error " + std::to_string(error); break;
        }
        Logger::getInstance().error(errorMsg, file, line);
    } else {
        trace("OpenGL operation successful: " + operation, file, line);
    }
}

void Logger::logMatrix(const std::string& name, const float* matrix, const std::string& file, int line) {
    std::stringstream ss;
    ss << name << " Matrix:\n";
    for (int i = 0; i < 4; i++) {
        ss << "  [";
        for (int j = 0; j < 4; j++) {
            ss << std::fixed << std::setprecision(3) << std::setw(8) << matrix[i * 4 + j];
            if (j < 3) ss << ", ";
        }
        ss << "]\n";
    }
    trace(ss.str(), file, line);
}

void Logger::logVector3(const std::string& name, float x, float y, float z, const std::string& file, int line) {
    std::stringstream ss;
    ss << name << ": (" << std::fixed << std::setprecision(3) << x << ", " << y << ", " << z << ")";
    trace(ss.str(), file, line);
}

void Logger::logRenderState(const std::string& file, int line) {
    std::stringstream ss;
    ss << "OpenGL Render State:\n";

    GLint viewport[4];
    glGetIntegerv(GL_VIEWPORT, viewport);
    ss << "  Viewport: " << viewport[0] << ", " << viewport[1] << ", " << viewport[2] << ", " << viewport[3] << "\n";

    GLboolean depthTest;
    glGetBooleanv(GL_DEPTH_TEST, &depthTest);
    ss << "  Depth Test: " << (depthTest ? "Enabled" : "Disabled") << "\n";

    GLint currentProgram;
    glGetIntegerv(GL_CURRENT_PROGRAM, &currentProgram);
    ss << "  Current Shader Program: " << currentProgram << "\n";

    GLint currentVAO;
    glGetIntegerv(GL_VERTEX_ARRAY_BINDING, &currentVAO);
    ss << "  Current VAO: " << currentVAO << "\n";

    trace(ss.str(), file, line);
}

std::string Logger::getTimestamp(const std::chrono::system_clock::time_point& time) {
    auto in_time_t = std::chrono::system_clock::to_time_t(time);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(time.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

std::string Logger::logLevelToString(LogLevel level) {
    switch (level) {
        case TRACE: return "TRACE";
        case DEBUG: return "DEBUG";
        case INFO: return " INFO";
        case WARNING: return " WARN";
        case ERROR: return "ERROR";
        case FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string Logger::getColorCode(LogLevel level) {
    if (!colorsEnabled_) return "";

    switch (level) {
        case TRACE: return "\033[90m";    // Dark gray
        case DEBUG: return "\033[36m";    // Cyan
        case INFO: return "\033[32m";     // Green
        case WARNING: return "\033[33m";  // Yellow
        case ERROR: return "\033[31m";    // Red
        case FATAL: return "\033[35m";    // Magenta
        default: return "";
    }
}

std::string Logger::getResetColorCode() {
    return colorsEnabled_ ? "\033[0m" : "";
}

void Logger::writeToOutputs(const LogEntry& entry) {
    std::string consoleMsg = formatLogEntry(entry, colorsEnabled_);
    std::string fileMsg = formatLogEntry(entry, false);

    if (consoleOutputEnabled_) {
        std::cout << consoleMsg << std::endl;
        std::cout.flush();
    }

    if (fileOutputEnabled_ && logFile_.is_open()) {
        logFile_ << fileMsg << std::endl;
        logFile_.flush();
    }
}

std::string Logger::formatLogEntry(const LogEntry& entry, bool useColors) {
    std::stringstream ss;

    if (useColors) {
        ss << getColorCode(entry.level);
    }

    if (timestampsEnabled_) {
        ss << "[" << getTimestamp(entry.timestamp) << "] ";
    }

    ss << "[" << logLevelToString(entry.level) << "]";

    if (!entry.category.empty()) {
        ss << "[" << entry.category << "]";
    }

    if (fileInfoEnabled_ && !entry.file.empty()) {
        // Extract just the filename from the full path
        std::string filename = entry.file;
        size_t lastSlash = filename.find_last_of("/\\");
        if (lastSlash != std::string::npos) {
            filename = filename.substr(lastSlash + 1);
        }
        ss << " (" << filename << ":" << entry.line << ")";
    }

    ss << " " << entry.message;

    if (useColors) {
        ss << getResetColorCode();
    }

    return ss.str();
}

// Configuration methods
void Logger::enableColors(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    colorsEnabled_ = enable;
}

void Logger::enableTimestamps(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    timestampsEnabled_ = enable;
}

void Logger::enableFileInfo(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    fileInfoEnabled_ = enable;
}

void Logger::flushAll() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logFile_.is_open()) {
        logFile_.flush();
    }
    if (traceFile_.is_open()) {
        traceFile_.flush();
    }
    std::cout.flush();
}

void Logger::printStatistics() {
    std::lock_guard<std::mutex> lock(mutex_);

    std::cout << "\n" << std::string(50, '=') << "\n";
    std::cout << "Logger Statistics:\n";
    std::cout << std::string(50, '=') << "\n";

    const char* levelNames[] = {"TRACE", "DEBUG", "INFO", "WARNING", "ERROR", "FATAL"};
    for (size_t i = 0; i < levelCounts_.size(); ++i) {
        std::cout << levelNames[i] << ": " << levelCounts_[i] << " messages\n";
    }

    std::cout << "Recent entries stored: " << recentEntries_.size() << "\n";
    std::cout << std::string(50, '=') << "\n\n";
}

std::vector<Logger::LogEntry> Logger::getRecentEntries(size_t count) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (count >= recentEntries_.size()) {
        return recentEntries_;
    }

    return std::vector<LogEntry>(recentEntries_.end() - count, recentEntries_.end());
}

} // namespace Utils